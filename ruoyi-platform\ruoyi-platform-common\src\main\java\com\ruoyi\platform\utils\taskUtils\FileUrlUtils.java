package com.ruoyi.platform.utils.taskUtils;

import static com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskStatus.*;

import java.net.URI;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.CacheUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.service.IPlatformModelService;
import com.ruoyi.platform.service.IPlatformVideoService;

/**
 * 文件URL处理工具类
 */
@Component
public class FileUrlUtils {

    private static final Logger log = LoggerFactory.getLogger(FileUrlUtils.class);

    @Autowired
    private IPlatformVideoService platformVideoService;

    @Autowired
    private IPlatformModelService platformModelService;

    // 从URL中提取文件存储路径（去除域名和临时凭证）
    public static String extractStoragePath(String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        try {
            if (url.startsWith("http://") || url.startsWith("https://")) {
                URL parsedUrl = new URI(url).toURL();
                String path = parsedUrl.getPath();
                if (path.startsWith("/")) {
                    path = path.substring(1);
                }
                return path;
            }
        } catch (Exception e) {
            log.debug("URL解析失败: {}", url);
        }
        return url;
    }

    // 为任务列表中的所有文件URL生成临时访问链接
    public static void processTasksUrls(List<PlatformVideo> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return;
        }
        tasks.forEach(task -> {
            try {
                // 处理驱动视频URL
                processUrl(task, task::getDrivenVideo, task::setDrivenVideo);
                // 处理驱动音频URL
                processUrl(task, task::getDrivenAudio, task::setDrivenAudio);
                // 处理结果视频URL
                processUrl(task, task::getResultVideo, task::setResultVideo);
            } catch (Exception e) {
                log.error("处理任务URL失败, ID: {}", task.getId(), e);
            }
        });
    }

    private static void processUrl(PlatformVideo task, Supplier<String> getUrl, Consumer<String> setUrl) throws Exception {
        String url = getUrl.get();
        if (StringUtils.isNotEmpty(url)) {
            String path = extractStoragePath(url);
            String tempUrl = FileOperateUtils.getURL(path);
            if (tempUrl != null) {
                setUrl.accept(tempUrl);
            }
        }
    }    

    // 处理上传的视频或音频文件
    public static Map<String, String> processUploadFile(MultipartFile file) {
        if (file == null || file.isEmpty() || file.getOriginalFilename() == null) {
            return null;
        }
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        String[] audioExtensions = {"mp3", "wav", "aac", "flac"};
        String[] videoExtensions = {"mp4", "avi", "rmvb", "mkv"};
        String mediaType;
        String contentType;
        String basePath;
        if (Arrays.asList(audioExtensions).contains(extension)) {
            mediaType = "audio";
            contentType = "audio/" + extension;
            basePath = "video/audio";
        } else if (Arrays.asList(videoExtensions).contains(extension)) {
            mediaType = "video";
            contentType = "video/" + extension;
            basePath = "video/result";
        } else {
            return null;
        }
        // 计算文件MD5
        String fileMd5 = Md5Utils.getMd5(file);
        // 检查缓存中是否已存在相同MD5的文件
        String cacheKey = "file_md5:" + fileMd5;
        if (CacheUtils.hasKey("file_upload_cache", cacheKey)) {
            // 如果存在，直接返回缓存的文件路径
            String cachedPath = CacheUtils.get("file_upload_cache", cacheKey, String.class);
            log.info("文件已存在，复用缓存路径: MD5={}, Path={}", fileMd5, cachedPath);
            Map<String, String> result = new HashMap<>();
            result.put("extension", extension);
            result.put("contentType", contentType);
            result.put("mediaType", mediaType);
            result.put("fullPath", cachedPath);
            result.put("isCached", "true"); // 标记为缓存复用
            return result;
        }

        // 如果不存在，生成新的文件路径
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        String timestampStr = String.valueOf(System.currentTimeMillis());
        String shortTimestamp = timestampStr.substring(Math.max(0, timestampStr.length() - 6));
        shortTimestamp = String.format("%06d", Long.parseLong(shortTimestamp)); // 固定6位补零
        String fileName = shortTimestamp + "_" + originalFilename;
        String fullPath = String.format("%s/%s/%s", basePath, currentDate, fileName);
        // 将新文件路径缓存到MD5映射中，设置24小时过期
        CacheUtils.put("file_upload_cache", cacheKey, fullPath, 24, TimeUnit.HOURS);
        log.info("新文件上传，缓存路径: MD5={}, Path={}", fileMd5, fullPath);
        Map<String, String> result = new HashMap<>();
        result.put("extension", extension);
        result.put("contentType", contentType);
        result.put("mediaType", mediaType);
        result.put("fullPath", fullPath);
        result.put("isCached", "false"); // 标记为新上传
        return result;
    }

    // 轮询待处理的视频合成任务
    @Scheduled(fixedDelay = 30000)
    public void pollPendingTasks() {
        try {
            List<PlatformVideo> pendingTasks = platformVideoService.selectPendingTasks();
            if (pendingTasks.isEmpty()) {
                log.info("没有待处理V版合成任务");
                return;
            }
            pendingTasks.forEach(this::processVideoTask);
        } catch (Exception e) {
            log.error("视频合成轮询异常: {}", e.getMessage());
        }
    }

    // 判断是否为V版任务
    public static boolean isVVersion(PlatformVideo task) {
        return task != null && StringUtils.isNotEmpty(task.getTaskNo()) && StringUtils.isNotEmpty(task.getModel());
    }

    // 处理单个视频任务（合并后）
    private void processVideoTask(PlatformVideo task) {
        try {
            if (isVVersion(task)) {
                if (!validateVTaskInfo(task)) {
                    return;
                }
                Map<String, Object> result = platformVideoService.queryVideoSynthesis(task.getTaskNo());
                updateTaskStatus(task, result);
            }
        } catch (Exception e) {
            throw new RuntimeException("V版任务处理失败", e);
        } finally {
            platformVideoService.updatePlatformVideo(task);
        }
    }

    // 验证V版任务基本信息
    private boolean validateVTaskInfo(PlatformVideo task) {
        JSONObject operation = task.getOperationJson();
        if (StringUtils.isEmpty(task.getTaskNo())) {
            setTaskFailed(task, operation, "缺少远程任务ID");
            return false;
        }
        if (StringUtils.isEmpty(task.getModel())) {
            setTaskFailed(task, operation, "缺少模型信息");
            return false;
        }
        return true;
    }

    private void setTaskFailed(PlatformVideo task, JSONObject operation, String message) {
        task.setStatus(STATUS_FAILED);
        operation.put("video_message", message);
        task.setOperation(operation.toString());
    }

    // 更新任务状态信息
    private void updateTaskStatus(PlatformVideo task, Map<String, Object> result) {
        JSONObject operation = task.getOperationJson();
        String code = String.valueOf(result.get("code"));
        String status = String.valueOf(result.get("status"));
        String message = String.valueOf(result.get("message"));

        if (code != null) {
            operation.put("code", code);
        }

        if ("200".equals(code)) {
            if (message != null) {
                operation.put("video_message", message);
            }
            if (StringUtils.isNotEmpty(status)) {
                if (STATUS_PENDING.equals(status) || STATUS_PROCESSING.equals(status)) {
                    task.setStatus(status);
                } else if (STATUS_SUCCESS.equals(status)) {
                    processSuccessStatus(task, result);
                } else if (STATUS_FAILED.equals(status)) {
                    task.setStatus(STATUS_FAILED);
                    log.info("视频合成失败 - ID: {}", task.getId());
                }
            }
        }
        task.setOperation(operation.toString());
    }

    // 处理成功状态的任务
    private void processSuccessStatus(PlatformVideo task, Map<String, Object> result) {
        task.setStatus(STATUS_SUCCESS);
        if (result.containsKey("originalStoragePath")) {
            String originalPath = String.valueOf(result.get("originalStoragePath"));
            int pathStart = originalPath.indexOf("video/result");
            if (pathStart != -1) {
                int queryStart = originalPath.indexOf("?", pathStart);
                if (queryStart != -1) {
                    originalPath = originalPath.substring(pathStart, queryStart);
                } else {
                    originalPath = originalPath.substring(pathStart);
                }
            }
            task.setResultVideo(originalPath);
            task.setCompleteAt(new Date());
        }
    }

    // 生成随机视频名称
    public static String generateVideoName() {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder randomString = new StringBuilder();
        for (int i = 0; i < 10; i++) {
            int index = (int) (Math.random() * characters.length());
            randomString.append(characters.charAt(index));
        }
        return randomString.toString();
    }

    // 获取指定任务的状态和结果视频URL
    public Map<String, Object> getTaskStatus(Long id) {
        PlatformVideo task = platformVideoService.selectPlatformVideoById(id);
        if (task == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("status", task.getStatus());
        result.put("completeAt", task.getCompleteAt());

        if (StringUtils.isNotEmpty(task.getResultVideo())) {
            try {
                String url = FileOperateUtils.getURL(task.getResultVideo());
                result.put("resultVideo", url != null ? url.toString() : null);
                if ("3".equals(task.getStatus()) && task.getCompleteAt() == null) {
                    task.setCompleteAt(DateUtils.getNowDate());
                    platformVideoService.updatePlatformVideo(task);
                }
            } catch (Exception e) {
                result.put("error", "无法生成访问链接");
            }
        } else {
            result.put("resultVideo", null);
        }
        return result;
    }

    // 获取一个待处理的任务
    public Map<String, Object> getOneTask(String version) {
        String lockKey = "platformVideoM:task:lock";
        String lockValue = UUID.randomUUID().toString();
        Map<String, Object> result = new HashMap<>();

        try {
            CacheUtils.putIfAbsent("platformVideoM", lockKey, lockValue);
            if (!CacheUtils.hasKey("platformVideoM", lockKey)) {
                result.put("message", "任务正在处理中");
                return result;
            }
            PlatformVideo task = platformVideoService.getOneTask(version);
            if (task == null) {
                return result;
            }

            JSONObject taskData = new JSONObject();
            taskData.put("id", task.getId());
            taskData.put("number", task.getNumber());
            processTaskUrlsForTask(task, taskData);
            extractOperationInfo(task, taskData);
            taskData.put("drivenVideoMd5", task.getDrivenVideoMd5());
            result.put("data", taskData);
        } catch (Exception e) {
            log.error("获取任务失败: {}", e.getMessage());
            result.put("error", e.getMessage());
        } finally {
            CacheUtils.remove("platformVideoM", lockKey);
        }
        return result;
    }
    // 获取一个待处理的任务
    public Map<String, Object> getOneTaskByVersion(String version) {
        String lockKey = "platformVideoH:task:lock";
        String lockValue = UUID.randomUUID().toString();
        Map<String, Object> result = new HashMap<>();

        try {
            CacheUtils.putIfAbsent("platformVideoH", lockKey, lockValue);
            if (!CacheUtils.hasKey("platformVideoH", lockKey)) {
                result.put("message", "任务正在处理中");
                return result;
            }
            PlatformVideo task = platformVideoService.getOneTaskByVersion(version);
            if (task == null) {
                return result;
            }

            JSONObject taskData = new JSONObject();
            taskData.put("id", task.getId());
            taskData.put("number", task.getNumber());
            processTaskUrlsForTask(task, taskData);
            extractOperationInfo(task, taskData);
            taskData.put("drivenVideoMd5", task.getDrivenVideoMd5());
            result.put("data", taskData);
        } catch (Exception e) {
            log.error("获取任务失败: {}", e.getMessage());
            result.put("error", e.getMessage());
        } finally {
            CacheUtils.remove("platformVideoH", lockKey);
        }
        return result;
    }

    private void processTaskUrlsForTask(PlatformVideo task, JSONObject taskData) {
        try {
            if (StringUtils.isNotEmpty(task.getDrivenAudio())) {
                String audioUrl = FileOperateUtils.getURL(task.getDrivenAudio());
                taskData.put("drivenAudio", audioUrl != null ? audioUrl.toString() : task.getDrivenAudio());
            }
            if (StringUtils.isNotEmpty(task.getDrivenVideo())) {
                String videoUrl = FileOperateUtils.getURL(task.getDrivenVideo());
                taskData.put("drivenVideo", videoUrl != null ? videoUrl.toString() : task.getDrivenVideo());
            }
            if (StringUtils.isNotEmpty(task.getResultVideo())) {
                String resultUrl = FileOperateUtils.getURL(task.getResultVideo());
                taskData.put("resultVideo", resultUrl != null ? resultUrl.toString() : task.getResultVideo());
            }
        } catch (Exception e) {
            log.debug("处理任务URL失败: {}", task.getId());
        }
    }

    // 从任务的operation字段中提取额外信息
    private void extractOperationInfo(PlatformVideo task, JSONObject taskData) {
        if (StringUtils.isNotEmpty(task.getOperation())) {
            try {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                taskData.put("drivenAudioMd5", operation.getString("driven_audio_md5"));
                taskData.put("drivenVideoMd5", operation.getString("driven_video_md5"));
                taskData.put("bboxShiftValue", operation.getString("bbox_shift_value"));
            } catch (Exception e) {
                log.debug("解析operation失败: {}", task.getId());
            }
        }
    }

    // 处理视频任务完成后的算力点扣除
    public void processHashrateDeduction(PlatformVideo task, String resultVideoPath) {
        if (task == null || resultVideoPath == null || resultVideoPath.isEmpty() ||
                !"3".equals(task.getStatus()) || StringUtils.isEmpty(task.getCreateBy())) {
            return;
        }
        if (StringUtils.isEmpty(task.getResultVideo())) {
            task.setResultVideo(resultVideoPath);
        }
        JSONObject operation = task.getOperationJson();
        if (operation != null && operation.getBooleanValue("fee_deducted", false)) {
            return;
        }
        boolean success = platformModelService.processTaskFeeDeduction(task);
        if (success) {
            platformVideoService.updatePlatformVideo(task);
        }
    }

    // 处理视频任务状态并扣除算力点
    public void processVideoTaskStatus(Map<String, Object> result, PlatformVideo task) {
        if (result == null || task == null ||
                !"3".equals(result.get("status")) || !"3".equals(task.getStatus())) {
            return;
        }
        JSONObject operation = task.getOperationJson();
        if (operation != null && operation.getBooleanValue("fee_deducted", false)) {
            return;
        }
        platformModelService.processTaskFeeDeduction(task);
    }

    // 统一处理任务完成后的算力点扣除逻辑
    public void processTaskCompletionAndFee(Map<String, Object> resultMap, String statusFieldName) {
        if (resultMap == null || !resultMap.containsKey(statusFieldName) ||
                !"3".equals(resultMap.get(statusFieldName).toString()) ||
                !resultMap.containsKey("taskId")) {
            return;
        }
        Long taskId = ((Number) resultMap.get("taskId")).longValue();
        PlatformVideo task = platformVideoService.selectPlatformVideoById(taskId);
        if (task != null) {
            processVideoTaskStatus(resultMap, task);
        }
    }
}    