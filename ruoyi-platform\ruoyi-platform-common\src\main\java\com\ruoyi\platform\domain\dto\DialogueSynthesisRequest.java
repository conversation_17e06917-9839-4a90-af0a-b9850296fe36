package com.ruoyi.platform.domain.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 数字人对话合成请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@Schema(description = "数字人对话合成请求")
public class DialogueSynthesisRequest {

    /** 数字人配置列表 */
    @Schema(description = "数字人配置列表")
    private List<DigitalHuman> digitalHumans;

    /** 对话内容列表 */
    @Schema(description = "对话内容列表")
    private List<DialogueContent> dialogueContent;

    /** 合成版本 (M版或H版) */
    @Schema(description = "合成版本")
    private String version = "H";

    /** M版专用：边界框偏移值 (范围: -7 到 +7) */
    @Schema(description = "M版专用：边界框偏移值，范围-7到+7")
    private Integer bboxShiftValue;

    /**
     * 数字人配置
     */
    @Data
    @Schema(description = "数字人配置")
    public static class DigitalHuman {
        /** 数字人ID */
        @Schema(description = "数字人ID")
        private String id;

        /** 数字人名称 */
        @Schema(description = "数字人名称")
        private String name;

        /** 形象名称 */
        @Schema(description = "形象名称")
        private String avatarName;

        /** 形象地址 */
        @Schema(description = "形象地址")
        private String avatarAddress;

        /** 声音ID (系统声音使用数字ID，内置音色可为空) */
        @Schema(description = "声音ID")
        private Long voiceId;

        /** 声音名称 (系统声音为数据库名称，内置音色为阿里云标识如zhiyuan) */
        @Schema(description = "声音名称")
        private String voiceName;

        /** 声音类型 (system: 系统声音, builtin: 内置音色) */
        @Schema(description = "声音类型")
        private String voiceType;
    }

    /**
     * 对话内容
     */
    @Data
    @Schema(description = "对话内容")
    public static class DialogueContent {
        /** 对话ID */
        @Schema(description = "对话ID")
        private Long id;

        /** 发言人ID */
        @Schema(description = "发言人ID")
        private String speaker;

        /** 发言人名称 */
        @Schema(description = "发言人名称")
        private String speakerName;

        /** 对话文本 */
        @Schema(description = "对话文本")
        private String text;

        /** 对话顺序 */
        @Schema(description = "对话顺序")
        private Integer order;
    }

    // Getter and Setter methods
    public List<DigitalHuman> getDigitalHumans() {
        return digitalHumans;
    }

    public void setDigitalHumans(List<DigitalHuman> digitalHumans) {
        this.digitalHumans = digitalHumans;
    }

    public List<DialogueContent> getDialogueContent() {
        return dialogueContent;
    }

    public void setDialogueContent(List<DialogueContent> dialogueContent) {
        this.dialogueContent = dialogueContent;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getBboxShiftValue() {
        return bboxShiftValue;
    }

    public void setBboxShiftValue(Integer bboxShiftValue) {
        this.bboxShiftValue = bboxShiftValue;
    }
}
