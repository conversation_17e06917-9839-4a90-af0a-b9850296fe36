package com.ruoyi.platform.domain.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 数字人对话合成请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@Schema(description = "数字人对话合成请求")
public class DialogueSynthesisRequest {

    /** 数字人配置列表 */
    @Schema(description = "数字人配置列表")
    private List<DigitalHuman> digitalHumans;

    /** 对话内容列表 */
    @Schema(description = "对话内容列表")
    private List<DialogueContent> dialogueContent;

    /**
     * 数字人配置
     */
    @Data
    @Schema(description = "数字人配置")
    public static class DigitalHuman {
        /** 数字人ID */
        @Schema(description = "数字人ID")
        private String id;

        /** 数字人名称 */
        @Schema(description = "数字人名称")
        private String name;

        /** 形象ID */
        @Schema(description = "形象ID")
        private Long avatarId;

        /** 形象名称 */
        @Schema(description = "形象名称")
        private String avatarName;

        /** 形象地址 */
        @Schema(description = "形象地址")
        private String avatarAddress;

        /** 声音ID */
        @Schema(description = "声音ID")
        private Long voiceId;

        /** 声音名称 */
        @Schema(description = "声音名称")
        private String voiceName;

        /** 声音类型 (system: 系统声音, custom: 内置音色) */
        @Schema(description = "声音类型")
        private String voiceType;
    }

    /**
     * 对话内容
     */
    @Data
    @Schema(description = "对话内容")
    public static class DialogueContent {
        /** 对话ID */
        @Schema(description = "对话ID")
        private Long id;

        /** 发言人ID */
        @Schema(description = "发言人ID")
        private String speaker;

        /** 发言人名称 */
        @Schema(description = "发言人名称")
        private String speakerName;

        /** 对话文本 */
        @Schema(description = "对话文本")
        private String text;

        /** 对话顺序 */
        @Schema(description = "对话顺序")
        private Integer order;
    }
}
