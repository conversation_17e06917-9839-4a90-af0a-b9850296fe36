package com.ruoyi.video.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.video.service.IBatchProducingService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 批量智能一键成片任务管理 前端控制器
 * <p>
 * 负责接收和处理与阿里云ICE批量智能一键成片相关的HTTP请求。
 * 提供RESTful API，用于提交任务、查询任务状态等操作。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Slf4j
@RestController
@RequestMapping("/video/BatchProducing")
@Tag(name = "【批量智能一键成片】")
public class BatchProducingController {

    @Autowired
    private IBatchProducingService batchProducingService;

    /**
     * 提交批量智能一键成片任务
     */
    @Operation(summary = "提交批量智能一键成片任务")
    @PostMapping("/submit")
    public AjaxResult submitBatchMediaProducingJob(
            @RequestParam("inputConfig") String inputConfig,
            @RequestParam("editingConfig") String editingConfig,
            @RequestParam("outputConfig") String outputConfig,
            @RequestParam(value = "userData", required = false) String userData,
            @RequestParam(value = "templateConfig", required = false) String templateConfig,
            @RequestParam(value = "clientToken", required = false) String clientToken) {

        try {
            if (isAnyEmpty(inputConfig, editingConfig, outputConfig)) {
                return AjaxResult.error("输入配置、剪辑配置和输出配置不能为空");
            }

            String result = batchProducingService.submitBatchMediaProducingJob(
                    inputConfig, editingConfig, outputConfig, userData, templateConfig, clientToken);
            return AjaxResult.success("批量智能一键成片任务提交成功", JSON.parse(result));
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return AjaxResult.error("批量智能一键成片任务提交失败: " + e.getMessage());
        }
    }

    /**
     * 获取批量智能一键成片任务详情
     */
    @Anonymous
    @Operation(summary = "获取批量智能一键成片任务详情")
    @GetMapping("/job/{jobId}")
    public AjaxResult getBatchMediaProducingJob(@PathVariable String jobId) {
        try {
            if (jobId == null || jobId.trim().isEmpty()) {
                return AjaxResult.error("作业ID不能为空");
            }
            String result = batchProducingService.getBatchMediaProducingJob(jobId);
            return AjaxResult.success("获取批量智能一键成片任务详情成功", JSON.parse(result));
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            return AjaxResult.error("获取批量智能一键成片任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取批量智能一键成片任务列表
     */
    @Operation(summary = "获取批量智能一键成片任务列表")
    @GetMapping("/list")
    public AjaxResult listBatchMediaProducingJobs(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "nextToken", required = false) String nextToken,
            @RequestParam(value = "JobType", required = false) String jobType) {

        try {
            String result = batchProducingService.listBatchMediaProducingJobs(
                    startTime, endTime, status, pageSize, nextToken, jobType);
            return AjaxResult.success("获取批量智能一键成片任务列表成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("获取批量智能一键成片任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 接收阿里云 ICE 批量智能一键成片任务完成回调
     */
    @Anonymous
    @Operation(summary = "接收阿里云 ICE 批量智能一键成片任务完成回调")
    @PostMapping("/callback")
    public AjaxResult receiveIceCallback(@RequestBody String callbackData) {
        try {
            com.alibaba.fastjson2.JSONObject callbackJson = JSON.parseObject(callbackData);
            String eventType = callbackJson.getString("EventType");

            if ("ProduceMediaComplete".equals(eventType)) {
                com.alibaba.fastjson2.JSONObject messageBody = callbackJson.getJSONObject("MessageBody");
                if (messageBody != null) {
                    String projectId = messageBody.getString("ProjectId");
                    String jobId = messageBody.getString("JobId");
                    String status = messageBody.getString("Status");
                    String mediaId = messageBody.getString("MediaId");
                    String mediaURL = messageBody.getString("MediaURL");

                    log.info("批量智能一键成片任务完成 - ProjectId: {}, JobId: {}, Status: {}, MediaId: {}, MediaURL: {}",
                            projectId, jobId, status, mediaId, mediaURL);

                    // TODO: 在这里添加您的业务逻辑

                    return AjaxResult.success("回调处理成功");
                } else {
                    return AjaxResult.error("回调消息体为空");
                }
            } else {
                return AjaxResult.success("回调已忽略");
            }
        } catch (Exception e) {
            return AjaxResult.error("回调处理失败: " + e.getMessage());
        }
    }

    /**
     * 判断多个字符串是否为空或空白
     */
    private boolean isAnyEmpty(String... strings) {
        for (String s : strings) {
            if (s == null || s.trim().isEmpty()) {
                return true;
            }
        }
        return false;
    }
}