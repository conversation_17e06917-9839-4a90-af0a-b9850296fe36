# 数字人对话合成API使用说明

## 概述

数字人对话合成功能允许用户创建多个数字人之间的对话视频，支持自定义形象、声音和对话内容。

## API接口

### 1. 创建数字人对话合成任务

**接口地址：** `POST /platform/video/dialogueSynthesis`

**请求参数：**

```json
{
  "digitalHumans": [
    {
      "id": "human_1",
      "name": "数字人1",
      "avatarId": 1,
      "avatarName": "形象1",
      "avatarAddress": "path/to/avatar1.mp4",
      "voiceId": 1,
      "voiceName": "声音1",
      "voiceType": "system"
    },
    {
      "id": "human_2", 
      "name": "数字人2",
      "avatarId": 2,
      "avatarName": "形象2",
      "avatarAddress": "path/to/avatar2.mp4",
      "voiceId": 2,
      "voiceName": "声音2",
      "voiceType": "custom"
    }
  ],
  "dialogueContent": [
    {
      "id": 1,
      "speaker": "human_1",
      "speakerName": "数字人1",
      "text": "你好，欢迎来到我们的产品介绍！",
      "order": 1
    },
    {
      "id": 2,
      "speaker": "human_2", 
      "speakerName": "数字人2",
      "text": "谢谢！我很期待了解更多关于这个产品的信息。",
      "order": 2
    }
  ]
}
```

**参数说明：**

- `digitalHumans`: 数字人配置列表
  - `id`: 数字人唯一标识
  - `name`: 数字人名称
  - `avatarId`: 形象ID（对应platform_image表）
  - `avatarName`: 形象名称
  - `avatarAddress`: 形象文件地址
  - `voiceId`: 声音ID（对应platform_sound表）
  - `voiceName`: 声音名称
  - `voiceType`: 声音类型（system: 系统声音, custom: 内置音色）

- `dialogueContent`: 对话内容列表
  - `id`: 对话唯一标识
  - `speaker`: 发言人ID（对应digitalHumans中的id）
  - `speakerName`: 发言人名称
  - `text`: 对话文本内容
  - `order`: 对话顺序

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": 123,
    "audioCount": 2,
    "videoCount": 2,
    "status": "processing",
    "message": "数字人对话合成任务已创建，正在处理中"
  }
}
```

### 2. 查询对话合成任务状态

**接口地址：** `GET /platform/video/dialogueSynthesis/status/{taskId}`

**路径参数：**
- `taskId`: 任务ID

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功", 
  "data": {
    "taskId": 123,
    "status": "1",
    "number": "VIDEO_20250129_001",
    "createTime": "2025-01-29 10:00:00",
    "completeTime": null,
    "resultVideo": null,
    "taskType": "dialogue_synthesis",
    "humanCount": 2,
    "dialogueCount": 2,
    "digitalHumans": [...],
    "dialogueContent": [...],
    "videoTasks": [...]
  }
}
```

**状态说明：**
- `1`: 待处理
- `2`: 处理中
- `3`: 成功
- `4`: 失败

## 业务流程

1. **音频合成阶段**
   - 根据对话文本和选定声音生成音频文件
   - 系统声音使用createTextToAudio接口
   - 内置音色使用synthesize接口

2. **视频合成阶段**
   - 基于音频文件和数字人形象生成视频片段
   - 使用H版视频合成接口

3. **任务管理**
   - 创建主任务记录跟踪整个对话合成过程
   - 记录所有子任务信息和状态

## 注意事项

1. 确保所有数字人形象和声音资源已经上传并可用
2. 对话文本长度建议控制在合理范围内
3. 建议数字人数量控制在2-6个之间
4. 任务创建后可通过状态查询接口跟踪进度

## 错误处理

常见错误及解决方案：

- `数字人配置不能为空`: 检查digitalHumans参数
- `对话内容不能为空`: 检查dialogueContent参数  
- `数字人形象不存在`: 检查avatarId是否有效
- `找不到发言人配置`: 检查speaker字段是否匹配digitalHumans中的id

## 示例代码

```javascript
// 创建对话合成任务
const createDialogue = async () => {
  const response = await fetch('/platform/video/dialogueSynthesis', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      digitalHumans: [
        {
          id: "human_1",
          name: "主持人",
          avatarId: 1,
          avatarName: "专业主持人形象",
          avatarAddress: "avatars/host.mp4",
          voiceId: 1,
          voiceName: "专业女声",
          voiceType: "system"
        }
      ],
      dialogueContent: [
        {
          id: 1,
          speaker: "human_1",
          speakerName: "主持人",
          text: "欢迎观看今天的节目！",
          order: 1
        }
      ]
    })
  });
  
  const result = await response.json();
  console.log('任务创建结果:', result);
};

// 查询任务状态
const checkStatus = async (taskId) => {
  const response = await fetch(`/platform/video/dialogueSynthesis/status/${taskId}`);
  const result = await response.json();
  console.log('任务状态:', result);
};
```
