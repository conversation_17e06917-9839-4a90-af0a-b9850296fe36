# 数字人对话合成API使用说明

## 概述

数字人对话合成功能直接利用现有的音频合成和视频合成业务，为每个对话文本生成对应的视频任务。支持M版和H版视频合成。

## API接口

### 创建数字人对话合成任务

**接口地址：** `POST /platform/video/dialogueSynthesis`

**请求参数：**

```json
{
  "version": "H",
  "digitalHumans": [
    {
      "id": "human_1",
      "name": "数字人1",
      "avatarName": "形象1",
      "avatarAddress": "video/image/2025/07/21/839236_研晨.mp4",
      "voiceId": 231,
      "voiceName": "主播4号",
      "voiceType": "system"
    },
    {
      "id": "human_2",
      "name": "数字人2",
      "avatarName": "形象2",
      "avatarAddress": "video/image/2025/07/22/601807_研晨.mp4",
      "voiceId": null,
      "voiceName": "zhiyuan",
      "voiceType": "builtin"
    }
  ],
  "dialogueContent": [
    {
      "id": 1,
      "speaker": "human_1",
      "speakerName": "数字人1",
      "text": "你好，欢迎来到我们的产品介绍！",
      "order": 1
    },
    {
      "id": 2,
      "speaker": "human_2",
      "speakerName": "数字人2",
      "text": "谢谢！我很期待了解更多关于这个产品的信息。",
      "order": 2
    }
  ]
}
```

**参数说明：**

- `version`: 合成版本（"M"、"H" 或 "V"，默认为 "H"）
- `bboxShiftValue`: M版专用参数，边界框偏移值（范围：-7到+7，可选）
- `model`: V版专用参数，模型编码（如"umi_video_v4"、"umi_video_v5"，V版必填）
- `digitalHumans`: 数字人配置列表

  - `id`: 数字人唯一标识
  - `name`: 数字人名称
  - `avatarName`: 形象名称
  - `avatarAddress`: 形象文件地址（直接传递形象地址，不需要ID）
  - `voiceId`: 声音ID（系统声音必填，内置音色可为null）
  - `voiceName`: 声音名称（系统声音为数据库名称，内置音色为阿里云标识）
  - `voiceType`: 声音类型（system: 系统声音, builtin: 内置音色）
- `dialogueContent`: 对话内容列表

  - `id`: 对话唯一标识
  - `speaker`: 发言人ID（对应digitalHumans中的id）
  - `speakerName`: 发言人名称
  - `text`: 对话文本内容
  - `order`: 对话顺序

## 版本差异说明

### H版（默认版本）
- 使用`synthesisH`方法创建任务
- 模型价位：500
- 支持系统声音和内置音色

### M版
- 使用`add`方法创建任务
- 模型价位：500
- 支持`bboxShiftValue`参数（范围：-7到+7）
- 支持系统声音和内置音色

### V版
- 使用V版API创建任务
- 模型价位：根据选择的模型动态确定（V6=500，V8=600）
- 必须指定`model`参数
- 支持的模型：
  - `umi_video_v4`（V6模型，价位500）
  - `umi_video_v5`（V8模型，价位600）

## V版请求示例

```json
{
  "version": "V",
  "model": "umi_video_v4",
  "digitalHumans": [
    {
      "id": "human_1",
      "name": "数字人1",
      "avatarName": "形象1",
      "avatarAddress": "video/image/2025/07/21/839236_研晨.mp4",
      "voiceId": null,
      "voiceName": "zhiyuan",
      "voiceType": "builtin"
    }
  ],
  "dialogueContent": [
    {
      "id": 1,
      "speaker": "human_1",
      "speakerName": "数字人1",
      "text": "欢迎使用V版数字人视频合成服务！",
      "order": 1
    }
  ]
}
```

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "videoTaskIds": [123, 124],
    "message": "数字人对话合成任务已创建"
  }
}
```

### 查询任务状态

使用现有的视频任务查询接口：

- `GET /platform/video/statusTaskNo/{taskNo}` - 根据任务编号查询
- `GET /platform/video/{id}` - 根据任务ID查询

## 支持的内置音色列表

内置音色使用阿里云TTS服务，支持以下音色：

| 音色名称 | 标识符   | 描述 |
| -------- | -------- | ---- |
| 知媛     | zhiyuan  | 女声 |
| 知悦     | zhiyue   | 女声 |
| 知莎     | zhisha   | 女声 |
| 知达     | zhida    | 男声 |
| 艾琪     | aiqi     | 女声 |
| 艾诚     | aicheng  | 男声 |
| 艾佳     | aijia    | 女声 |
| 思琪     | siqi     | 女声 |
| 思佳     | sijia    | 女声 |
| 马树     | mashu    | 男声 |
| 悦儿     | yueer    | 女声 |
| 若兮     | ruoxi    | 女声 |
| 艾达     | aida     | 女声 |
| 思诚     | sicheng  | 男声 |
| 宁儿     | ninger   | 女声 |
| 小云     | xiaoyun  | 女声 |
| 小刚     | xiaogang | 男声 |
| 瑞琳     | ruilin   | 女声 |

## 业务流程

1. **音频合成阶段**

   - 根据对话文本和选定声音生成音频文件
   - 系统声音：调用现有的createTextToAudio业务方法（使用数据库中的声音配置）
   - 内置音色：调用现有的synthesize业务方法（使用阿里云TTS服务）
   - 自动下载音频文件并上传到存储服务
   - 计算音频文件MD5值
2. **视频合成阶段**

   - 基于音频文件和数字人形象创建视频合成任务
   - 直接使用形象地址，无需查询形象ID
   - M版调用现有的add()方法
   - H版调用现有的synthesisH()方法
   - 自动设置音频MD5到视频任务
   - 自动查询并设置形象MD5
   - 根据版本设置模型价位（V版=600，M版/H版=500）
3. **任务管理**

   - 直接使用现有的视频任务管理系统
   - 每个对话生成一个独立的视频任务
   - 可通过现有接口查询任务状态

## 技术特点

1. **复用现有业务**：完全基于现有的音频合成和视频合成业务
2. **支持M/H版本**：根据version参数选择不同的视频合成方式
3. **灵活的声音支持**：同时支持系统声音和内置音色
4. **独立任务管理**：每个对话生成独立的视频任务，便于管理和监控
5. **真实文件处理**：下载音频文件并上传到存储服务，计算真实MD5值
6. **严格错误处理**：任何步骤失败都会直接抛出异常，不使用模拟数据
7. **完整元数据记录**：在operation字段中记录完整的任务信息和MD5值

## 注意事项

1. 确保所有数字人形象和声音资源已经上传并可用
2. 对话文本长度建议控制在合理范围内
3. 建议数字人数量控制在2-6个之间
4. 使用现有的视频任务查询接口跟踪进度

## 示例代码

```javascript
// 创建对话合成任务
const createDialogue = async () => {
  const response = await fetch('/platform/video/dialogueSynthesis', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      version: "H", // 或 "M"
      digitalHumans: [
        {
          id: "human_1",
          name: "主持人",
          avatarName: "专业主持人形象",
          avatarAddress: "video/image/2025/07/22/601807_主持人.mp4",
          voiceId: 1,
          voiceName: "专业女声",
          voiceType: "system"
        }
      ],
      dialogueContent: [
        {
          id: 1,
          speaker: "human_1",
          speakerName: "主持人",
          text: "欢迎观看今天的节目！",
          order: 1
        }
      ]
    })
  });

  const result = await response.json();
  console.log('任务创建结果:', result);
  // result.data.videoTaskIds 包含所有创建的视频任务ID
};

// 查询任务状态（使用现有接口）
const checkStatus = async (taskNo) => {
  const response = await fetch(`/platform/video/statusTaskNo/${taskNo}`);
  const result = await response.json();
  console.log('任务状态:', result);
};
```

## Operation字段详细说明

在数字人对话合成功能中，每个视频任务的 `operation`字段会存储完整的任务元数据，JSON格式如下：

```json
{
  "dialogue_id": 2,
  "speaker_id": "human_2",
  "speaker_name": "数字人2",
  "text": "谢谢！我很期待了解更多关于这个产品的信息。",
  "avatar_name": "形象2",
  "voice_name": "zhiyuan",
  "voice_type": "builtin",
  "task_type": "dialogue_synthesis_item",
  "driven_audio_md5": "6f2ecd76aadb1de6bae808cfecc7b31e",
  "driven_video_md5": "464d631187cc7fb4fa4a24e97a509f60",
  "model_price": "500",
  "bbox_shift_value": 3
}
```

### 字段说明

- `dialogue_id`: 对话内容ID
- `speaker_id`: 说话人ID
- `speaker_name`: 说话人名称
- `text`: 对话文本内容
- `avatar_name`: 数字人形象名称
- `voice_name`: 声音名称
- `voice_type`: 声音类型（system/builtin）
- `task_type`: 任务类型标识
- `driven_audio_md5`: 音频文件MD5值
- `driven_video_md5`: 形象文件MD5值
- `model_price`: 模型价位（V版=600，M版/H版=500）
- `bbox_shift_value`: M版专用边界框偏移值（范围：-7到+7，仅M版包含此字段）
