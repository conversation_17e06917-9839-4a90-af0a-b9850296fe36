package com.ruoyi.platform.model.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformArticle;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.mapper.PlatformArticleMapper;
import com.ruoyi.platform.mapper.PlatformAudioMapper;
import com.ruoyi.platform.model.domain.PlatformMachineCode;
import com.ruoyi.platform.model.domain.PlatformSound;
import com.ruoyi.platform.model.domain.PlatformTask;
import com.ruoyi.platform.model.domain.TtsRequest;
import com.ruoyi.platform.model.mapper.PlatformMachineCodeMapper;
import com.ruoyi.platform.model.mapper.PlatformSoundMapper;
import com.ruoyi.platform.model.mapper.PlatformTaskMapper;
import com.ruoyi.platform.model.service.IPlatformSoundService;
import com.ruoyi.platform.model.service.IPlatformTaskService;
import com.ruoyi.platform.model.utils.ModelException;
import com.ruoyi.platform.utils.taskUtils.MultipartFileUtils;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 任务管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-24
 */
@Service
public class PlatformTaskServiceImpl implements IPlatformTaskService {

    private static final Logger log = LoggerFactory.getLogger(PlatformTaskServiceImpl.class);

    @Value("${aliyun.voice.app-key}")
    private String appKey;

    @Value("#{@nlsAccessToken}")  // 直接注入Token Bean
    private String dynamicToken;

    @Value("${aliyun.voice.nls.endpoint:nls-gateway.cn-shanghai.aliyuncs.com}")
    private String endpoint;

    @PostConstruct
    public void init() {
        log.info("初始化 Aliyun-voice 配置: appKey={}, endpoint={}", appKey, endpoint);
        if (appKey == null || dynamicToken == null || endpoint == null) {
            throw new ServiceException("阿里云语音配置未正确加载");
        }
    }

    @Autowired
    private PlatformTaskMapper platformTaskMapper;

    @Autowired
    private PlatformSoundMapper platformSoundMapper;

    @Autowired
    private PlatformArticleMapper platformArticleMapper;

    @Autowired
    private PlatformAudioMapper platformAudioMapper;

    @Autowired
    private IPlatformSoundService platformSoundService;

    @Autowired
    private PlatformMachineCodeMapper platformMachineCodeMapper;

    /**
     * 查询任务管理
     * 
     * @param taskId 任务管理主键
     * @return 任务管理
     */
    @Override
    public PlatformTask selectPlatformTaskByTaskId(Long taskId) {
        return platformTaskMapper.selectPlatformTaskByTaskId(taskId);
    }

    /**
     * 查询任务管理列表
     * 
     * @param platformTask 任务管理
     * @return 任务管理
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PlatformTask> selectPlatformTaskList(PlatformTask platformTask) {
        return platformTaskMapper.selectPlatformTaskList(platformTask);
    }

    /**
     * 新增任务管理
     * 
     * @param platformTask 任务管理
     * @return 结果
     */
    @Override
    public int insertPlatformTask(PlatformTask platformTask) {
        platformTask.setCreateTime(DateUtils.getNowDate());
        platformTask.setTaskName(IdUtils.fastUUID());
        return platformTaskMapper.insertPlatformTask(platformTask);
    }

    /**
     * 修改任务管理
     * 
     * @param platformTask 任务管理
     * @return 结果
     */
    @Override
    public int updatePlatformTask(PlatformTask platformTask) {
        platformTask.setUpdateTime(DateUtils.getNowDate());
        return platformTaskMapper.updatePlatformTask(platformTask);
    }

    /**
     * 批量删除任务管理
     * 
     * @param taskIds 需要删除的任务管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformTaskByTaskIds(Long[] taskIds) {
        return platformTaskMapper.deletePlatformTaskByTaskIds(taskIds);
    }

    /**
     * 删除任务管理信息
     * 
     * @param taskId 任务管理主键
     * @return 结果
     */
    @Override
    public int deletePlatformTaskByTaskId(Long taskId) {
        return platformTaskMapper.deletePlatformTaskByTaskId(taskId);
    }

    // 获取任务队列
    @Override
    public PlatformTask selectPlatformmachineCode(String machineCode) {
        // 使用行级锁查询待处理任务
        List<PlatformTask> tasks = platformTaskMapper.selectPlatformTaskListSuccess();
        if (tasks.isEmpty()) {
            throw new ModelException("没有待处理的任务");
        }
        for (PlatformTask task : tasks) {
            task.setTaskStatus("1"); // 将状态更新为处理中
            // 更新任务状态
            int updatedRows = platformTaskMapper.updateStateByStatusAndCode(task.getTaskId(), machineCode);
            if (updatedRows > 0) {
                return task; // 返回更新后的任务对象
            }
        }
        throw new ModelException("任务被抢占"); // 如果没有任何可更新的任务抛出异常
    }

    // 创建文本转音频任务
    @Override
    public Long createTTSAudio(PlatformArticle[] platformArticle, Long modelName) {
        String username = SecurityUtils.getUsername();
        Date nowDate = DateUtils.getNowDate();
        PlatformTask platformTask = new PlatformTask();
        platformTask.setTaskName(IdUtils.fastSimpleUUID());
        platformTask.setType("1");
        platformTask.setModelName(modelName.toString());
        platformTask.setCreateBy(username);
        platformTask.setTaskStatus("0");
        platformTask.setUpdateBy(username);
        platformTask.setCreateTime(nowDate);
        platformTask.setUpdateTime(nowDate);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("task_name", platformTask.getTaskName());
        jsonObject.put("type", platformTask.getType());
        jsonObject.put("model_name", platformTask.getModelName());
        jsonObject.put("callback", "/tts/callback");
        JSONObject config = new JSONObject();
        JSONObject tts = new JSONObject();
        PlatformSound platformSound = platformSoundMapper.selectPlatformSoundBySoundId(modelName);
        tts.put("ref_audio", platformSound.getSoundRef());
        tts.put("prompt_text", platformSound.getSoundRefText());
        JSONArray text = new JSONArray();
        for (int i = 0; i < platformArticle.length; i++) {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put(platformArticle[i].getArticleId().toString(), platformArticle[i].getContent());
            text.add(jsonObject1);
        }
        tts.put("text", text);
        config.put("tts", tts);
        jsonObject.put("configs", config);
        platformTask.setTaskContent(jsonObject.toString());
        platformTaskMapper.insertPlatformTask(platformTask);
        return platformTask.getTaskId();
    }

    // 文本转音频任务回调
    @Override
    public int ttsCallback(PlatformTask platformTask) {
        String result = platformTask.getTaskResult();
        if (result != null) {
            platformTask.setTaskStatus("2");
            PlatformSound platformSound = platformSoundMapper
                    .selectPlatformSoundBySoundId(Long.valueOf(platformTask.getModelName()));
            JSONObject jsonObject = JSONObject.parseObject(result);
            JSONArray text = jsonObject.getJSONArray("text");
            for (int i = 0; i < text.size(); i++) {
                JSONObject o = text.getJSONObject(i);
                o.keySet().forEach(key -> {
                    PlatformArticle platformArticle = platformArticleMapper
                            .selectPlatformArticleByArticleId(Long.valueOf(key));
                    String audioPath = o.getString(key);
                    PlatformAudio exist = platformAudioMapper.getAudioDetailByAddress(audioPath);
                    if (exist != null) {
                        // 只更新业务字段，不覆盖md5
                        exist.setAudioFrom("1");
                        exist.setSoundId(platformSound.getSoundId());
                        exist.setCategoryId(platformArticle.getCategoryId());
                        exist.setArticleId(platformArticle.getArticleId());
                        exist.setAudioName(platformSound.getSoundName() + "_" + platformArticle.getArticleId());
                        String username = platformArticle.getCreateBy();
                        Date nowDate = DateUtils.getNowDate();
                        exist.setCreateBy(username);
                        exist.setCreateTime(nowDate);
                        exist.setUpdateBy(username);
                        exist.setUpdateTime(nowDate);
                        platformAudioMapper.updatePlatformAudio(exist);
                    } else {
                        // 新增完整业务数据（md5可能为null，后续上传时会补全）
                        PlatformAudio platformAudio = new PlatformAudio();
                        String username = platformArticle.getCreateBy();
                        Date nowDate = DateUtils.getNowDate();
                        platformAudio.setCreateBy(username);
                        platformAudio.setCreateTime(nowDate);
                        platformAudio.setUpdateTime(nowDate);
                        platformAudio.setUpdateBy(username);
                        platformAudio.setAudioPath(audioPath);
                        platformAudio.setAudioFrom("1");
                        platformAudio.setSoundId(platformSound.getSoundId());
                        platformAudio.setCategoryId(platformArticle.getCategoryId());
                        platformAudio.setArticleId(platformArticle.getArticleId());
                        platformAudio.setAudioName(platformSound.getSoundName() + "_" + platformArticle.getArticleId());
                        platformAudioMapper.insertPlatformAudio(platformAudio);
                    }
                });
            }
        } else {
            platformTask.setTaskStatus("3");
        }
        return platformTaskMapper.updatePlatformTask(platformTask);
    }

    // 创建一个训练声音任务
    @Override
    public Long createTaskSAudio(Long modelName, String trainPath) {
        String username = SecurityUtils.getUsername();
        Date nowDate = DateUtils.getNowDate();
        PlatformTask platformTask = new PlatformTask();
        platformTask.setTaskName(IdUtils.fastSimpleUUID());
        platformTask.setType("0");
        platformTask.setModelName(modelName.toString());
        platformTask.setCreateBy(username);
        platformTask.setTaskStatus("0");
        platformTask.setUpdateBy(username);
        platformTask.setCreateTime(nowDate);
        platformTask.setUpdateTime(nowDate);

        // 创建 JSON 对象
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("task_name", platformTask.getTaskName());
        jsonObject.put("type", platformTask.getType());
        jsonObject.put("model_name", platformTask.getModelName());
        jsonObject.put("callback", "/train/callback");

        // 创建配置对象
        JSONObject config = new JSONObject();

        // 创建 input_voice 数组并添加 trainPath URL
        JSONArray inputVoiceArray = new JSONArray();
        if (trainPath != null && !trainPath.isEmpty()) {
            inputVoiceArray.add(trainPath); // 添加 trainPath 的 URL
        }

        // 将 input_voice 加入配置对象
        config.put("input_voice", inputVoiceArray);
        jsonObject.put("configs", config);
        platformTask.setTaskContent(jsonObject.toString());
        platformTaskMapper.insertPlatformTask(platformTask);
        return platformTask.getTaskId();
    }

    // 训练声音任务回调
    @Override
    public int taskCallback(PlatformTask platformTask) {
        String taskStatus = platformTask.getTaskStatus();
        Long soundId = Long.valueOf(platformTask.getModelName());
        // 根据声音ID查询声音信息
        PlatformSound platformSound = platformSoundMapper.selectPlatformSoundBySoundId(soundId);
        if (platformSound == null) {
            throw new ModelException("任务对应的声音已经被删除");
        }
        if ("2".equals(taskStatus)) {
            // 任务成功
            String result = platformTask.getTaskResult();
            JSONObject jsonObject = JSONObject.parseObject(result);
            platformSound.setSoundGpt(jsonObject.getString("gptModelPath"));
            platformSound.setSoundSovits(jsonObject.getString("sovitsModelPath"));
        } else {
            taskStatus = "3";
        }
        platformSound.setSoundStatus(taskStatus);
        // 更新数据库中的声音状态
        int updateSoundResult = platformSoundService.updatePlatformSound(platformSound);
        if (updateSoundResult <= 0) {
            throw new ModelException("更新声音状态失败，声音ID: " + soundId);
        }
        return platformTaskMapper.updatePlatformTask(platformTask);
    }

    // 模型服务上传训练后的声音模型文件: ModelCheckpoint/sound/xxxx.ckpt
    @Override
    public Object uploadModelSoundFile(MultipartFile gptModel, MultipartFile sovitsModel, Long taskId, Long soundId,
            String machineCode) throws Exception {
        try {
            // 检查gptModel文件是否为空
            if (gptModel == null || gptModel.isEmpty()) {
                throw new ModelException("请上传gptckpt模型文件");
            }
            // 检查sovitsModel文件是否为空
            if (sovitsModel == null || sovitsModel.isEmpty()) {
                throw new ModelException("请上传sovitspth模型文件");
            }
            // 限制文件大小
            long gptModelSize = 500 * 1024 * 1024; // 500MB
            long sovitsModelSize = 500 * 1024 * 1024; // 500MB
            // 检查文件大小（大于规定大小则拒绝上传）
            if (gptModel.getSize() > gptModelSize) {
                throw new ModelException("gptckpt模型文件不能超过500MB");
            }
            if (sovitsModel.getSize() > sovitsModelSize) {
                throw new ModelException("sovitspth模型文件不能超过500MB");
            }
            // 验证机器码是否有效
            PlatformMachineCode machineCodeObject = platformMachineCodeMapper
                    .selectPlatformMachineCodeByMachineCode(machineCode);
            if (machineCodeObject == null) {
                throw new ModelException("无效的机器码，无法上传文件。");
            }
            // 获取机器码状态
            Long machineCodeStatus = machineCodeObject.getMachineCodeStatus();
            // 验证机器码状态是否为“正常”（1表示正常）
            if (machineCodeStatus == 0L) {
                throw new ModelException("机器码状态为禁用，无法上传音频文件。");
            }
            // 获取文件名
            String gptModelFileName = gptModel.getOriginalFilename(); // gptModel
            String sovitsModelFileName = sovitsModel.getOriginalFilename(); // sovitsModel
            // 设置文件路径，后台直接写死
            String filePath = "ModelCheckpoint/sound"; // minio上传路径
            // 获取时间戳
            long timestamp = System.currentTimeMillis();
            // 处理gptModel文件
            String uniqueGptFileName = timestamp + "_" + gptModelFileName;
            String fullGptPath = filePath + "/" + uniqueGptFileName;
            FileOperateUtils.upload(fullGptPath, gptModel, null);
            // 处理sovitsModel文件
            String uniqueSovitsFileName = timestamp + "_" + sovitsModelFileName;
            String fullSovitsPath = filePath + "/" + uniqueSovitsFileName;
            FileOperateUtils.upload(fullSovitsPath, sovitsModel, null);
            // 创建 PlatformSound 实例并设置属性
            Date nowDate = DateUtils.getNowDate(); // 获取时间
            PlatformSound sound = new PlatformSound();
            sound.setSoundId(soundId);// 需要更新的声音Id
            sound.setSoundGpt(fullGptPath); // gpt文件
            sound.setSoundSovits(fullSovitsPath); // sovits文件
            sound.setUpdateTime(nowDate); // 更新时间
            // 更新数据库数据库
            platformSoundService.updateModelSoundService(sound);
            // 创建返回结果
            Map<String, String> result = new HashMap<>();
            result.put("gptModelPath", fullGptPath);
            result.put("sovitsModelPath", fullSovitsPath);
            // 返回成功响应，并返回上传后的路径
            return result;
        } catch (IOException e) {
            throw new ModelException("上传模型文件失败: " + e.getMessage()); // 返回失败响应
        }
    }

    // 通用下载音频文件
    @Override
    public void downloadAudio(String machineCode, String fileUrl, HttpServletResponse response) throws Exception {
        // 检查 fileUrl 是否有效
        if (StringUtils.isEmpty(fileUrl)) {
            throw new ModelException("音频文件地址无效");
        }
        // 验证机器码是否有效
        PlatformMachineCode machineCodeObject = platformMachineCodeMapper
                .selectPlatformMachineCodeByMachineCode(machineCode);
        if (machineCodeObject == null) {
            throw new ModelException("无效的机器码，无法下载文件。");
        }
        // 获取机器码状态
        Long machineCodeStatus = machineCodeObject.getMachineCodeStatus();
        // 验证机器码状态是否为“正常”（1表示正常）
        if (machineCodeStatus == 0L) {
            throw new ModelException("机器码状态为禁用，无法下载音频文件。");
        }
        // 设置响应类型和文件名
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        try (OutputStream outputStream = response.getOutputStream()) {
            // 处理下载时候文件重新编码
            FileUtils.setAttachmentResponseHeader(response, FileUtils.getName(fileUrl));
            // 从指定的 URL 下载文件
            FileOperateUtils.downLoad(fileUrl, outputStream);
            outputStream.flush(); // 确保所有数据都写入输出流
        } catch (IOException e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.getWriter().write("下载失败，请稍后重试: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // 通过机器码下载音频
    @Override
    public void downloadAudioByMachineCode(String machineCode, String fileUrl, HttpServletResponse response)
            throws Exception {
        // 检查 machineCode 和 fileUrl 是否有效
        if (StringUtils.isEmpty(machineCode) || StringUtils.isEmpty(fileUrl)) {
            throw new ModelException("参数无效！请输入正确的参数，再进行下载");
        }
        // 验证机器码是否有效
        PlatformMachineCode machineCodeObject = platformMachineCodeMapper
                .selectPlatformMachineCodeByMachineCode(machineCode);
        if (machineCodeObject == null) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value()); // 设置401状态码
            response.getWriter().write("无效的机器码，无法下载文件。");
            return; // 终止方法执行
        }
        // 获取机器码状态
        Long machineCodeStatus = machineCodeObject.getMachineCodeStatus();
        // 验证机器码状态 0 禁用 1 正常
        if (machineCodeStatus == 0L) {
            throw new ModelException("机器码状态为禁用，无法下载音频文件。");
        }
        // 设置响应类型和文件名
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        try (OutputStream outputStream = response.getOutputStream()) {
            // 处理下载时候文件重新编码
            FileUtils.setAttachmentResponseHeader(response, FileUtils.getName(fileUrl));
            // 从指定的 URL 下载文件
            FileOperateUtils.downLoad(fileUrl, outputStream);
            outputStream.flush(); // 确保所有数据都写入输出流
        } catch (FileNotFoundException e) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.getWriter().write("文件未找到，请检查文件路径是否正确: " + e.getMessage());
        } catch (IOException e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.getWriter().write("下载失败，请稍后重试: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // 模型上传服务后的推理音频文件
    @Override
    public Object uploadModelAudioFile(MultipartFile audioFile, Long taskId, String machineCode) throws Exception {
        try {
            // 检查训练音频文件是否为空
            if (audioFile == null || audioFile.isEmpty()) {
                throw new ModelException("请上传训练音频文件");
            }
            // 限制文件大小
            long audioFileSize = 60 * 1024 * 1024; // 60MB
            // 检查文件大小（大于规定大小则拒绝上传）
            if (audioFile.getSize() > audioFileSize) {
                throw new ModelException("音频文件不能超过60MB");
            }
            // 验证机器码是否有效
            PlatformMachineCode machineCodeObject = platformMachineCodeMapper
                    .selectPlatformMachineCodeByMachineCode(machineCode);
            if (machineCodeObject == null) {
                throw new ModelException("无效的机器码，无法上传文件。");
            }
            // 获取机器码状态
            Long machineCodeStatus = machineCodeObject.getMachineCodeStatus();
            // 验证机器码状态是否为“正常”（1表示正常）
            if (machineCodeStatus == 0L) {
                throw new ModelException("机器码状态为禁用，无法上传文件。");
            }
            // 获取文件名
            String audioFileName = audioFile.getOriginalFilename();
            // 设置文件路径，后台直接写死
            String filePath = "ModelCheckpoint/sound/" + taskId; // minio上传路径
            // 处理上传的音频文件
            String uniqueTrainFileName = System.currentTimeMillis() + "_" + audioFileName;
            String audioPath = filePath + "/" + uniqueTrainFileName;
            FileOperateUtils.upload(audioPath, audioFile, null); // 文件上传
            // 处理返回结果
            Map<String, String> result = new HashMap<>();
            result.put("audioPath", audioPath);
            return result; // 返回成功响应
        } catch (IOException e) {
            throw new ModelException("上传文件失败: " + e.getMessage()); // 返回失败响应
        }
    }

    // 推理音频上传
    @Override
    public String uploadTask(MultipartFile file, Long taskId, String machineCode) throws Exception {
        try {
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                throw new ModelException("请上传训练音频文件");
            }
            // 验证机器码是否有效
            PlatformMachineCode machineCodeObject = platformMachineCodeMapper
                    .selectPlatformMachineCodeByMachineCode(machineCode);
            if (machineCodeObject == null) {
                throw new ModelException("无效的机器码，无法上传文件。");
            }
            // 获取机器码状态
            Long machineCodeStatus = machineCodeObject.getMachineCodeStatus();
            // 验证机器码状态是否为“正常”（1表示正常）
            if (machineCodeStatus == 0L) {
                throw new ModelException("机器码状态为禁用，无法上传文件。");
            }
            // 构建文件名
            String fileName = file.getOriginalFilename();
            String uniqueFileName = System.currentTimeMillis() + "_" + fileName;
            // 更新上传路径格式
            String filePath = "ModelTasks/" + machineCode + "/" + taskId + "/" + uniqueFileName;
            // 上传文件到 oss
            FileOperateUtils.upload(filePath, file, null);
            // 计算MD5
            String md5 = com.ruoyi.common.utils.sign.Md5Utils.getMd5(file);
            // 检查是否已存在该音频（以audioPath为唯一）
            PlatformAudio exist = platformAudioMapper.getAudioDetailByAddress(filePath);
            if (exist != null) {
                // 只更新md5，不新增
                exist.setAudioMd5(md5);
                platformAudioMapper.updatePlatformAudio(exist);
            } else {
                // 新增只带md5的记录
                PlatformAudio audio = new PlatformAudio();
                audio.setAudioPath(filePath);
                audio.setAudioMd5(md5);
                platformAudioMapper.insertPlatformAudio(audio);
            }
            return filePath;
        } catch (IOException e) {
            throw new ModelException("文件上传失败：" + e.getMessage());
        }
    }

    // 创建文本转音频任务
    @Override
    public Long createTextToAudio(String text, Long modelName, Long categoryId) {
        PlatformArticle platformArticle = buildPlatformArticle(text, categoryId);
        int insertResult = platformArticleMapper.insertArticle(platformArticle);
        if (insertResult <= 0 ) {
            throw new ModelException("文案新增失败");
        }
        PlatformArticle[] articles = {platformArticle};
        return createTTSAudio(articles, modelName);
    }

    /**
     * 构建 PlatformArticle 对象
     */
    private PlatformArticle buildPlatformArticle(String text, Long categoryId) {
        String username = SecurityUtils.getUsername();
        Date nowDate = DateUtils.getNowDate();
        PlatformArticle platformArticle = new PlatformArticle();
        platformArticle.setContent(text);
        platformArticle.setCreateBy(username);
        platformArticle.setCreateTime(nowDate);
        platformArticle.setUpdateBy(username);
        platformArticle.setUpdateTime(nowDate);
        platformArticle.setRemark("语音合成");
        platformArticle.setCategoryId(categoryId);
        return platformArticle;
    }

    @Override
    public String synthesize(TtsRequest request) {
        NlsClient client = null;
        try {
            // 创建NLS客户端 - 使用动态Token
            String url = "wss://" + endpoint + "/ws/v1";
            client = new NlsClient(url, dynamicToken);  // 使用动态Token初始化
            // 用于收集音频数据的输出流
            ByteArrayOutputStream audioDataStream = new ByteArrayOutputStream();
            CountDownLatch latch = new CountDownLatch(1);
            // 创建语音合成实例
            SpeechSynthesizer synthesizer = new SpeechSynthesizer(client, getSynthesizerListener(audioDataStream, latch));
            // 配置参数
            synthesizer.setAppKey(appKey);
            String format = request.getFormat() != null ? request.getFormat().toUpperCase() : "WAV";
            synthesizer.setFormat(OutputFormatEnum.valueOf(format));
            int sampleRate = request.getSampleRate() != 0 ? request.getSampleRate() : 16000;
            SampleRateEnum rate = SampleRateEnum.SAMPLE_RATE_16K;
            switch (sampleRate) {
                case 8000: rate = SampleRateEnum.SAMPLE_RATE_8K; break;
                case 16000: rate = SampleRateEnum.SAMPLE_RATE_16K; break;
                case 22050:
                case 24000: rate = SampleRateEnum.SAMPLE_RATE_16K; break;
                default: rate = SampleRateEnum.SAMPLE_RATE_16K;
            }
            synthesizer.setSampleRate(rate);
            String voice = request.getVoice() != null ? request.getVoice() : "siyue";
            synthesizer.setVoice(voice);
            int volume = request.getVolume() != 0 ? request.getVolume() : 50;
            Integer speechRate = request.getSpeechRate() != null ? request.getSpeechRate() : 0;
            int pitchRate = request.getPitchRate() != 0 ? request.getPitchRate() : 0;
            synthesizer.setVolume(volume);
            synthesizer.setSpeechRate(speechRate);
            synthesizer.setPitchRate(pitchRate);
            synthesizer.setText(request.getText());
            synthesizer.start();// 启动合成
            log.info("语音合成已启动，等待完成...");
            latch.await(60, TimeUnit.SECONDS);
            // 获取音频数据
            byte[] audioData = audioDataStream.toByteArray();
            if (audioData.length == 0) {
                log.error("语音合成返回的音频数据为空");
                return null;
            }
            String md5 = DigestUtils.md5Hex(audioData);
            // 检查是否已存在该音频
            PlatformAudio exist = platformAudioMapper.getAudioMd5(md5);
            if (exist != null) {
                log.info("该音频已存在，ID: {}, URL: {}", exist.getAudioId(), exist.getAudioPath());
                String audioUrl = FileOperateUtils.getURL(exist.getAudioPath());
                return audioUrl;
            }

            // 生成文件名
            String fileExtension = format.toLowerCase();
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = timestamp + "." + fileExtension;

            // 构建文件存储路径
            String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String filePath = "ModelTasks/audio/" + currentDate + "/" + fileName;

            // 创建MultipartFile并上传
            String contentType = getMimeTypeFromExtension(fileExtension);
            MultipartFile multipartFile = MultipartFileUtils.createFromBytes(audioData, fileName, fileName, contentType);
            FileOperateUtils.upload(filePath, multipartFile, null);
            // 获取可访问的URL
            String audioUrl = FileOperateUtils.getURL(filePath);
            // 创建并插入文案记录
            PlatformArticle platformArticle = buildPlatformArticle(request.getText(), null);
            int articleResult = platformArticleMapper.insertArticle(platformArticle);
            if (articleResult <= 0) {
                throw new ModelException("文案新增失败");
            }
            // 获取插入后的 articleId
            Long articleId = platformArticle.getArticleId();
            if (articleId == null) {
                throw new ModelException("获取文案ID失败");
            }
            // 创建并插入音频记录
            PlatformAudio platformAudio = new PlatformAudio();
            String username = SecurityUtils.getUsername();
            Date nowDate = DateUtils.getNowDate();
            platformAudio.setAudioMd5(md5);
            platformAudio.setAudioName(request.getVoice());
            platformAudio.setAudioFrom("1"); // 表示由TTS合成
            platformAudio.setAudioPath(filePath);
            platformAudio.setArticleId(articleId);
            platformAudio.setCategoryId(null);
            platformAudio.setCreateBy(username);
            platformAudio.setCreateTime(nowDate);
            platformAudio.setUpdateBy(username);
            platformAudio.setUpdateTime(nowDate);
            int audioResult = platformAudioMapper.insertPlatformAudio(platformAudio);
            if (audioResult <= 0) {
                throw new ModelException("音频记录新增失败");
            }
            return audioUrl;
        } catch (Exception e) {
            log.error("语音合成过程发生异常", e);
            return null;
        } finally {
            if (client != null) {
                client.shutdown();
                log.debug("NLS客户端已关闭");
            }
        }
    }

    private SpeechSynthesizerListener getSynthesizerListener(
        ByteArrayOutputStream audioDataStream, 
        CountDownLatch latch) {
        return new SpeechSynthesizerListener() {
            @Override
            public void onComplete(SpeechSynthesizerResponse response) {
                log.info("语音合成完成，状态码: {}, 状态信息: {}, TaskId: {}", 
                    response.getStatus(), response.getStatusText(), response.getTaskId());
                latch.countDown();
            }
            // 保留 ByteBuffer 版本的 onMessage
            @Override
            public void onMessage(ByteBuffer data) {
                try {
                    byte[] bytes = new byte[data.remaining()];
                    data.get(bytes);
                    audioDataStream.write(bytes);
                } catch (Exception e) {
                    log.error("写入ByteBuffer音频数据失败", e);
                }
            }
            @Override
            public void onFail(SpeechSynthesizerResponse response) {
                log.error("语音合成失败，状态码: {}, 错误信息: {}, TaskId: {}", 
                    response.getStatus(), response.getStatusText(), response.getTaskId());
                latch.countDown();
            }
        };
    }

    /**
     * 根据文件扩展名获取MIME类型
     * 使用已有的MimeTypeUtils工具类
     */
    private String getMimeTypeFromExtension(String extension) {
        switch (extension.toLowerCase()) {
            case "wav":
                return "audio/wav";
            case "mp3":
                return "audio/mpeg";
            case "pcm":
                return "audio/pcm";
            case "ogg":
                return "audio/ogg";
            default:
                return "audio/" + extension.toLowerCase();
        }
    }
}
