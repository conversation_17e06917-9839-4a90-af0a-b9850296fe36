spring:
  data:
    # redis 配置
    redis:
      # 生产环境 localhost password：szbadmin port：6379
      # 公司内部环境 r-m5e8lr2lh6snpw3d8epd.redis.rds.aliyuncs.com password：<PERSON><PERSON><PERSON>B<PERSON>@Redis port：6379
      # 研发环境算力机 ***************  password：szbadmin port：6379
      # host: r-m5e8lr2lh6snpw3d8epd.redis.rds.aliyuncs.com
      host: ***************
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 0
      # 密码
      password: szbadmin
      #password: <PERSON>ZhiBao@Redis
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms