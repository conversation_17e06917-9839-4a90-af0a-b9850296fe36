<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.platform.mapper.PlatformImageMapper">

    <resultMap type="PlatformImage" id="PlatformImageResult">
        <result property="imageId" column="image_id" />
        <result property="imageName" column="image_name" />
        <result property="imageCode" column="image_code" />
        <result property="imageStatus" column="image_status" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
        <result property="imageAddress" column="image_address" />
        <result property="md5" column="video_md5" />
    </resultMap>

    <sql id="selectPlatformImageVo">
        select image_id, image_name, image_code, image_status, i.create_by, i.create_time, i.update_by, i.update_time, i.remark, i.image_address, i.video_md5 from platform_image i left join sys_user u on u.user_name = i.create_by left join  sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPlatformImageList" parameterType="PlatformImage" resultMap="PlatformImageResult">
        <include refid="selectPlatformImageVo"/>
        <where>
            <if test="imageName != null  and imageName != ''"> and image_name like concat('%', #{imageName}, '%')</if>
            <if test="imageStatus != null  and imageStatus != ''"> and image_status = #{imageStatus}</if>
            ${params.dataScope}
        </where>
        order by create_time desc
    </select>

    <select id="selectPlatformImageByImageId" parameterType="Long" resultMap="PlatformImageResult">
        <include refid="selectPlatformImageVo"/>
        where image_id = #{imageId}
    </select>

    <insert id="insertPlatformImage" parameterType="PlatformImage" useGeneratedKeys="true" keyProperty="imageId">
        insert into platform_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imageName != null">image_name,</if>
            <if test="imageCode != null">image_code,</if>
            <if test="imageStatus != null">image_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="imageAddress != null">image_address,</if>
            <if test="md5 != null">video_md5,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imageName != null">#{imageName},</if>
            <if test="imageCode != null">#{imageCode},</if>
            <if test="imageStatus != null">#{imageStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="imageAddress != null">#{imageAddress},</if>
            <if test="md5 != null">#{md5},</if>
        </trim>
    </insert>

    <update id="updatePlatformImage" parameterType="PlatformImage">
        update platform_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="imageName != null">image_name = #{imageName},</if>
            <if test="imageCode != null">image_code = #{imageCode},</if>
            <if test="imageStatus != null">image_status = #{imageStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="imageAddress != null">image_address = #{imageAddress},</if>
        </trim>
        where image_id = #{imageId}
    </update>

    <delete id="deletePlatformImageByImageId" parameterType="Long">
        delete from platform_image where image_id = #{imageId}
    </delete>

    <delete id="deletePlatformImageByImageIds" parameterType="String">
        delete from platform_image where image_id in 
        <foreach item="imageId" collection="array" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </delete>

    <!-- 根据多个形象管理主键查询形象管理列表 -->
    <select id="selectPlatformImagesByImageIds">
        SELECT * FROM platform_image WHERE image_id IN
        <foreach item="id" index="index" collection="imageIds" open="(" separator="," close=")">
                #{id}
        </foreach>
    </select>

    <select id="selectImagesByCondition" parameterType="map" resultMap="PlatformImageResult">
        SELECT image_id, image_name, image_status, image_address 
        FROM platform_image 
        <where>
            <if test="imageAddress != null">
                AND image_address = #{imageAddress}
            </if>
            <if test="fileNamePattern != null">
                AND image_address LIKE #{fileNamePattern}
            </if>
            <if test="video_md5 != null">
                AND video_md5 = #{video_md5}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>
</mapper>