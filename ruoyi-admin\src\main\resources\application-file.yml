#121 oss配置 研发环境桶：shuzhibao-test，生产环境桶：szb-resource LTAI5tGdT6qWzDA6mkkuYz9j ******************************
#118 oss配置 研发环境桶：szb-test-bucket，生产环境桶：szb-pc LTAI5tPwLQ5XjHWaKBM6rrk3 ******************************
oss:
  enable: false
  primary: MASTER
  client:
    MASTER:
      permission: private
      accessKeyId: LTAI5tPwLQ5XjHWaKBM6rrk3
      accessKeySecret: ******************************
      bucketName: szb-pc
      endpoint: oss-cn-beijing.aliyuncs.com

# Minio配置
minio:
  enable: true
  primary: MASTER
  client:
    MASTER:
      permission: private
      url: http://192.168.110.201:9000
      accessKey: minioadmin
      secretKey: minioadmin
      bucketName: plaform
      
# local配置
local:
  enable: false
  primary: MASTER
  client:
    MASTER:
      permission: private 
      path: /data/files/master
      # 建议public权限的api以 /profile开头就不需要再从SecurityConfig配置权限了
      api: /profile/files/master
   
# 第三方服务V版数字人视频合成
video:
  synthesis:
    server-url: http://120.25.121.138:3001  # 视频合成服务器地址
    synthesis-path: /v1/chat/completions  # 创建视频合成任务路径
    query-path: /v1/chat/completions      # 视频合成结果查询路径
    api-key: sk-veFlJSn7wKHlC7DLqr1ESZVHnEsGvOlgoO8CrUURYUG93Mws # API密钥