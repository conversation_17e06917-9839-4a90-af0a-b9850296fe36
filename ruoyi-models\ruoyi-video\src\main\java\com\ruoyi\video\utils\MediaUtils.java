package com.ruoyi.video.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.regex.Pattern;

/**
 * 媒资相关工具类
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public class MediaUtils {

    // 文件类型常量
    public static final String TYPE_VIDEO = "video";
    public static final String TYPE_AUDIO = "audio";
    public static final String TYPE_IMAGE = "image";
    public static final String TYPE_TEXT = "text";
    public static final String TYPE_OTHER = "other";
    
    // 日期格式常量
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    
    // 文件类型匹配模式
    private static final Pattern VIDEO_EXT_PATTERN = Pattern.compile("(?i)^(mp4|avi|mkv|mov|wmv|flv|webm|m4v)$");
    private static final Pattern AUDIO_EXT_PATTERN = Pattern.compile("(?i)^(mp3|wav|flac|aac|m4a|wma|ogg)$");
    private static final Pattern IMAGE_EXT_PATTERN = Pattern.compile("(?i)^(jpg|jpeg|png|gif|bmp|webp|svg)$");
    private static final Pattern TEXT_EXT_PATTERN = Pattern.compile("(?i)^(txt|srt|ass|vtt|sub)$");

    /**
     * 根据文件扩展名确定文件类型
     * 
     * @param ext 文件扩展名
     * @return 文件类型：video、audio、image、text、other
     */
    public static String determineFileType(String ext) {
        if (ext == null || ext.isEmpty()) {
            return TYPE_OTHER;
        }
        
        String cleanExt = ext.toLowerCase().trim();
        
        if (VIDEO_EXT_PATTERN.matcher(cleanExt).matches()) {
            return TYPE_VIDEO;
        } else if (AUDIO_EXT_PATTERN.matcher(cleanExt).matches()) {
            return TYPE_AUDIO;
        } else if (IMAGE_EXT_PATTERN.matcher(cleanExt).matches()) {
            return TYPE_IMAGE;
        } else if (TEXT_EXT_PATTERN.matcher(cleanExt).matches()) {
            return TYPE_TEXT;
        } else {
            return TYPE_OTHER;
        }
    }

    /**
     * 构建框架分类路径
     * 
     * 路径格式：{category}/{date}/{timestamp}_{fileName}
     * 例如：video/2025/07/12/1720780800000_example.mp4
     * 
     * @param category 分类名称，如：video、audio、image等
     * @param fileName 原始文件名
     * @return 构建后的分类路径
     */
    public static String buildFrameworkCategoryPath(String category, String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        String currentDate = LocalDate.now().format(DATE_FORMATTER);
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        return String.format("%s/%s/%s_%s", 
                sanitizeCategory(category), 
                currentDate, 
                timestamp, 
                fileName);
    }

    /**
     * 构建完整的OSS URL
     * 
     * OSS URL格式：oss://bucketName/filePath
     * 
     * @param bucketName OSS桶名
     * @param filePath 文件路径
     * @return 完整的OSS URL
     */
    public static String buildOssUrl(String bucketName, String filePath) {
        if (bucketName == null || bucketName.isEmpty()) {
            throw new IllegalArgumentException("OSS桶名不能为空");
        }
        
        String cleanPath = sanitizeFilePath(filePath);
        return String.format("oss://%s/%s", bucketName, cleanPath);
    }

    /**
     * 从文件名中提取扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名（不包含点），如果没有扩展名则返回空字符串
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 验证文件扩展名是否为支持的媒资类型
     * 
     * @param ext 文件扩展名
     * @return true表示支持，false表示不支持
     */
    public static boolean isSupportedMediaType(String ext) {
        return !TYPE_OTHER.equals(determineFileType(ext));
    }

    /**
     * 构建ICE媒资库的完整路径
     * 
     * 标准格式：ice/MediaLibrary/{category}/{date}/{timestamp}_{fileName}
     * 
     * @param category 媒资分类
     * @param fileName 文件名
     * @return ICE媒资库路径
     */
    public static String buildIceMediaLibraryPath(String category, String fileName) {
        String frameworkPath = buildFrameworkCategoryPath(category, fileName);
        return "ice/MediaLibrary/" + frameworkPath;
    }
    
    /**
     * 清理分类名称，确保其有效性
     */
    private static String sanitizeCategory(String category) {
        if (category == null || category.isEmpty()) {
            return TYPE_OTHER;
        }
        
        // 移除特殊字符，只保留字母、数字和下划线
        return category.replaceAll("[^a-zA-Z0-9_]", "_");
    }
    
    /**
     * 清理文件路径，确保格式正确
     */
    private static String sanitizeFilePath(String filePath) {
        if (filePath == null) {
            return "";
        }
        
        // 移除开头的斜杠
        String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;
        
        // 规范化路径分隔符
        return cleanPath.replace('\\', '/');
    }
}