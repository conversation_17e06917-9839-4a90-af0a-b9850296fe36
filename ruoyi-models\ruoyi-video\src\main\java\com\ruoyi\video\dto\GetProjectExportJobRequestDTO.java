package com.ruoyi.video.dto;

import lombok.Data;

/**
 * 查询云剪辑工程导出任务请求DTO
 * <p>
 * 用于封装查询云剪辑工程导出任务状态和结果的请求参数。
 * 对应阿里云ICE API的 GetProjectExportJob 接口。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
public class GetProjectExportJobRequestDTO {

    /**
     * 工程导出任务 ID
     * <p>
     * 必填参数，通过 SubmitProjectExportJob 接口返回的 JobId
     * </p>
     */
    private String jobId;

    /**
     * 构造函数
     */
    public GetProjectExportJobRequestDTO() {
    }

    /**
     * 构造函数
     * 
     * @param jobId 工程导出任务ID
     */
    public GetProjectExportJobRequestDTO(String jobId) {
        this.jobId = jobId;
    }
}
