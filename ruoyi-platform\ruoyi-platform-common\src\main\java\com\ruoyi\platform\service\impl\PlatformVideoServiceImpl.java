package com.ruoyi.platform.service.impl;

import static com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskStatus.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.domain.PlatformImage;
import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.domain.dto.DialogueSynthesisRequest;
import com.ruoyi.platform.mapper.PlatformModelMapper;
import com.ruoyi.platform.mapper.PlatformVideoMapper;
import com.ruoyi.platform.service.IPlatformVideoService;
import com.ruoyi.platform.utils.taskUtils.FileUrlUtils;
import com.ruoyi.platform.utils.taskUtils.HttpClientUtil;
import com.ruoyi.platform.utils.taskUtils.MultipartFileUtils;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoConfig;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskVersion;

/**
 * 视频合成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Service
public class PlatformVideoServiceImpl implements IPlatformVideoService 
{
    private static final Logger log = LoggerFactory.getLogger(PlatformVideoServiceImpl.class);

    @Autowired
    private PlatformVideoConfig platformVideoConfig;

    @Autowired
    private PlatformVideoMapper platformVideoMapper;

    @Autowired
    private PlatformModelMapper platformModelMapper;

    @Autowired
    private com.ruoyi.platform.mapper.PlatformAudioMapper platformAudioMapper;

    @Autowired
    private com.ruoyi.platform.mapper.PlatformImageMapper platformImageMapper;

    private static final String PARAM_MODEL = "model";
    private static final String PARAM_MESSAGES = "messages";
    private static final String PARAM_CONTENT = "content"; 

    //查询视频合成
    @Override
    public PlatformVideo selectPlatformVideoById(Long id){
        return platformVideoMapper.selectPlatformVideoById(id);
    }

    // 查询视频合成列表
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PlatformVideo> selectPlatformVideoList(PlatformVideo platformVideo) {
        List<PlatformVideo> videoList = platformVideoMapper.selectPlatformVideoList(platformVideo);
        return videoList;
    }    

    //修改视频合成
    @Override
    public int updatePlatformVideo(PlatformVideo platformVideo){
        return platformVideoMapper.updatePlatformVideo(platformVideo);
    }

    //批量删除视频合成
    @Override
    public int deletePlatformVideoByIds(Long[] ids){
        for (Long id : ids) {
            PlatformVideo video = selectPlatformVideoById(id);
            if (video != null && StringUtils.isNotEmpty(video.getResultVideo())) {
                if ("2".equals(video.getStatus())) {
                    throw new ServiceException("任务正在处理中，不能删除！");  // 为处理中不让删除
                }
                try {
                    FileOperateUtils.deleteFile(video.getResultVideo()); // 只删除结果视频
                } catch (Exception e) {
                    log.error("删除文件失败", e);
                }
            }
        }
        return platformVideoMapper.deletePlatformVideoByIds(ids);
    }

    // 查询状态为可用的模型
    @Override
    public List<PlatformModel> getAvailableModels() {
        PlatformModel queryModel = new PlatformModel(); 
        queryModel.setModelStatus(1); //0 禁用 1=可用
        return platformModelMapper.selectWyModelList(queryModel);
    }

    //查询待处理和处理中的任务
    @Override
    public List<PlatformVideo> selectPendingTasks() {
        PlatformVideo queryTask = new PlatformVideo();
        queryTask.setStatus(STATUS_PENDING);  
        List<PlatformVideo> pendingTasks = platformVideoMapper.selectPlatformVideoList(queryTask);
        queryTask.setStatus(STATUS_PROCESSING);
        List<PlatformVideo> processingTasks = platformVideoMapper.selectPlatformVideoList(queryTask);
        pendingTasks.addAll(processingTasks);
        return pendingTasks;
    }


    //创建一条视频合成任务
    @Override
    public Long add(PlatformVideo platformVideo) {
        platformVideo.setNumber(FileUrlUtils.generateVideoName()); //任务编号
        platformVideo.setCreateBy(SecurityUtils.getUsername()); //创建人
        platformVideo.setUpdateBy(SecurityUtils.getUsername()); //修改人
        platformVideo.setCreatedAt(DateUtils.getNowDate()); //创建时间
        platformVideo.setUpdatedAt(DateUtils.getNowDate()); //修改时间
        platformVideo.setVersion(PlatformVideoTaskVersion.M_VERSION); // 设置版本号
        platformVideoMapper.insertPlatformVideo(platformVideo);
        Long taskId = platformVideo.getId();
        return taskId;
    }

    //查询视频合成单个任务
    @Override
    public PlatformVideo getOneTask(String version) {
        PlatformVideo condition = new PlatformVideo();
        condition.setStatus( "1"); // 1待处理 2处理中 3成功 4失败',
        condition.setVersion(version); // 版本号
        PlatformVideo musetalkTask = platformVideoMapper.selectPlatformVideoOne(condition);
        if (musetalkTask == null) {
            log.info("没有待处理M版合成任务");
            return null;
        }
        log.info("获取到待处理任务，任务编号为：{}", musetalkTask.getNumber());
        musetalkTask.setStatus("2"); // 改为 处理中
        platformVideoMapper.updatePlatformVideo(musetalkTask);
        return musetalkTask;
    }

    //根据版本查询视频合成单个任务
    @Override
    public PlatformVideo getOneTaskByVersion(String version) {
        PlatformVideo condition = new PlatformVideo();
        condition.setStatus( "1"); // 1待处理 2处理中 3成功 4失败',
        condition.setVersion(version); // 版本号
        PlatformVideo task = platformVideoMapper.selectPlatformVideoOne(condition);
        if (task == null) {
            log.info("没有待处理"+version+"版合成任务");
            return null;
        }
        log.info("获取到待处理任务，任务编号为：{}", task.getNumber());
        task.setStatus("2"); // 改为 处理中
        platformVideoMapper.updatePlatformVideo(task);
        return task;
    }

    //根据动态任务结果调整任务的状态
    @Override
    public void updateTaskStatus(Long id, Long status, String resultVideo) {
        PlatformVideo platformVideo = new PlatformVideo();
        platformVideo.setId(id);
        platformVideo.setStatus(String.valueOf(status));
        platformVideo.setResultVideo(resultVideo);
        if (status == 4) {
            JSONObject operation = new JSONObject(); // 处理失败时的错误信息 - 存入JSON
            operation.put("errorResult", "Task processing failed"); 
            platformVideo.setOperation(operation.toString());
        }
        if (status == 3) {
            platformVideo.setCompleteAt(DateUtils.getNowDate()); //设置视频合成的完成时间
        }
        platformVideoMapper.updatePlatformVideo(platformVideo);
    }

    //上传音频文件
    @Override
    public Object uploadAudioFile(MultipartFile file){
        try {
            if (file == null || file.isEmpty()) {
                throw new ServiceException("音频素材不能为空！");
            }
            long maxFileSize = 100 * 1024 * 1024; // 100MB
            if (file.getSize() > maxFileSize) {
                throw new ServiceException("音频素材文件不能超过100MB！");
            }
            String documentFileName = file.getOriginalFilename();
            String filePath = "video/audio"; // 音频素材文件存储位置
            long timestamp = System.currentTimeMillis();
            String uniqueFileName = timestamp + "_" + documentFileName;
            String fullPath = filePath + "/" + uniqueFileName;
            fullPath = FileOperateUtils.upload(fullPath, file, null);
            String md5 = Md5Utils.getMd5(file);
            Map<String, String> result = new HashMap<>();
            result.put("url", fullPath);
            result.put("md5", md5);
            JSONObject operation = new JSONObject();
            operation.put("driven_audio_md5", md5);
            result.put("operation", operation.toString());
            return result; 
        } catch (IOException e) {
            throw new ServiceException("上传音频文件失败: " + e.getMessage()); // 返回失败响应queryVideoTask
        }
    }

    //查询视频合成任务(Map参数版本) v版
    @SuppressWarnings("unchecked")
    public PlatformVideo queryVideoTask(String model, Long taskId, Map<String, Object> params) {
        if (params != null) {
            if (!params.containsKey("messages") || !params.containsKey("model")) {
                throw new ServiceException("请求参数不完整");
            }
            List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get("messages");
            if (messages == null || messages.isEmpty()) {
                throw new ServiceException("messages不能为空");
            }
            Map<String, Object> content = (Map<String, Object>) messages.get(0).get("content");
            if (content == null || !content.containsKey("task_id")) {
                throw new ServiceException("task_id不能为空");
            }
            model = params.get("model").toString();
            taskId = Long.valueOf(content.get("task_id").toString());
        }
        PlatformVideo task = selectPlatformVideoById(taskId);
        if (task == null) {
            throw new ServiceException("任务不存在");
        }
        return task;
    }

    //上传媒体文件(视频或音频)
    @Override
    public Map<String, String> uploadMedia(MultipartFile file, String type) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }
        if (file.getSize() > 500 * 1024 * 1024) {
            throw new ServiceException("文件不能超过500MB");
        }
        // 根据类型选择不同的存储基础路径
        String basePath;
        if ("video".equals(type)) {
            basePath = "video/result";
        } else if ("audio".equals(type)) {
            basePath = "video/audio";
        } else {
            basePath = "video/other";
        }
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

        // 使用时间戳的后6位作为前缀
        String timestamp = String.valueOf(System.currentTimeMillis());
        String shortTimestamp = timestamp.substring(Math.max(0, timestamp.length() - 6)); // 取后六位
        shortTimestamp = String.format("%06d", Long.parseLong(shortTimestamp)); // 补零保证6位
        String originalFilename = file.getOriginalFilename();
        String newFileName = shortTimestamp + "_" + originalFilename;
        String fullPath = String.format("%s/%s/%s", basePath, currentDate, newFileName);
        String savedPath = FileOperateUtils.upload(fullPath, file, null);
        // 构建结果
        Map<String, String> result = new HashMap<>();
        result.put("savedPath", fullPath);     // 真实存储路径，用于数据库存储
        result.put("url", savedPath);          // 临时访问URL，用于前端展示
        result.put("type", type);               // 文件类型
        return result;
    }

    //创建数字人视频合成任务 v版
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> createVideoSynthesisWithUrls(Map<String, Object> params) throws Exception {
        Map<String, Object> originalParams = JSON.parseObject(JSON.toJSONString(params));
        if (!params.containsKey(PARAM_MODEL)) {
            throw new ServiceException("缺少模型编码");
        }
        String modelCode = params.get(PARAM_MODEL).toString();
        PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
        if (modelInfo == null || !modelInfo.getModelStatus().equals(1)) {
            throw new ServiceException("模型不可用");
        }
        List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get(PARAM_MESSAGES);
        if (messages != null && !messages.isEmpty()) {
            Map<String, Object> content = (Map<String, Object>) messages.get(0).get(PARAM_CONTENT);
            if (content != null) {
                if (content.containsKey("live_video_url")) {
                    String videoPath = content.get("live_video_url").toString();
                    String originalPath = FileUrlUtils.extractStoragePath(videoPath);
                    if (StringUtils.isEmpty(originalPath)) {
                        throw new ServiceException("视频路径无效");
                    }
                    String tempUrl = FileOperateUtils.getURL(originalPath);
                    if (tempUrl != null) {
                        content.put("live_video_url", tempUrl);
                    }
                }
                if (content.containsKey("live_sound_url")) {
                    String audioPath = content.get("live_sound_url").toString();
                    String originalPath = FileUrlUtils.extractStoragePath(audioPath);
                    String tempUrl = FileOperateUtils.getURL(originalPath);
                    if (tempUrl != null) {
                        content.put("live_sound_url", tempUrl);
                    }
                }
            }
        }
        Map<String, Object> result = createVideoSynthesis(params);
        saveVideoTask(originalParams, modelCode, result);
        return result;
    }

    @SuppressWarnings("unchecked")
    private void saveVideoTask(Map<String, Object> params, String modelCode, Map<String, Object> result) {
        try {
            PlatformVideo task = new PlatformVideo();
            task.setModel(modelCode);
            task.setTaskNo(result.get("task_id").toString());
            task.setNumber(FileUrlUtils.generateVideoName());
            task.setVersion("V");
            task.setStatus(result.getOrDefault("status", STATUS_PENDING).toString());

            String username = SecurityUtils.getUsername();
            Date nowDate = DateUtils.getNowDate();
            task.setCreateBy(username);
            task.setUpdateBy(username);
            task.setCreatedAt(nowDate);
            task.setUpdatedAt(nowDate);

            if (params.containsKey(PARAM_MESSAGES)) {
                List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get(PARAM_MESSAGES);
                if (!messages.isEmpty()) {
                    Map<String, Object> content = (Map<String, Object>) messages.get(0).get(PARAM_CONTENT);
                    if (content != null) {
                        if (content.containsKey("live_video_url")) {
                            String videoPath = content.get("live_video_url").toString();
                            String originalPath = FileUrlUtils.extractStoragePath(videoPath);
                            task.setDrivenVideo(originalPath);
                        }
                        if (content.containsKey("live_sound_url")) {
                            String audioPath = content.get("live_sound_url").toString();
                            String originalPath = FileUrlUtils.extractStoragePath(audioPath);
                            task.setDrivenAudio(originalPath);
                        }
                        task.setCallbackUrl(content.getOrDefault("callback_url", "").toString());
                    }
                }
            }
            PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
            JSONObject jsonBuilder = new JSONObject();
            jsonBuilder.put("video_priority", "0");
            jsonBuilder.put("code", result.getOrDefault("code", "200"));
            jsonBuilder.put("video_message", result.getOrDefault("msg", "任务创建成功"));
            if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                jsonBuilder.put("model_price", modelInfo.getModelVersion());
            }
            String jsonStr = jsonBuilder.toJSONString();
            task.setOperation(jsonStr);
            int rows = platformVideoMapper.insertPlatformVideo(task);
            if (rows <= 0) {
                throw new ServiceException("保存任务记录失败");
            }
        } catch (Exception e) {
            throw new ServiceException("保存任务记录失败: " + e.getMessage());
        }
    }

    // 从临时凭证URL下载视频并重新上传到对象存储
    public static String downloadAndSaveVideo(String temporaryUrl) throws Exception {
        String tempFileName = UUID.randomUUID().toString() + ".mp4"; // 创建临时文件
        String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + tempFileName;
        File tempFile = new File(tempFilePath);
        try {
            URL url = new URI(temporaryUrl).toURL();
            URLConnection conn = url.openConnection();
            conn.setConnectTimeout(30000);  // 设置30秒连接超时
            conn.setReadTimeout(120000);    // 设置120秒读取超时
            try (InputStream inputStream = conn.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = timestamp + ".mp4";
            String address = "video/result";
            String fullPath = String.format("%s/%s/%s", address, currentDate, fileName);
            MultipartFile multipartFile = MultipartFileUtils.createFromFile(
                tempFile, "file", fileName, "video/mp4");
            String savedPath = FileOperateUtils.upload(fullPath, multipartFile);
            return savedPath;
        } catch (Exception e) {
            throw e;
        } finally {
            if (tempFile.exists()) {
                if (!tempFile.delete()) {
                    log.warn("临时文件删除失败: {}", tempFilePath); // 清理临时文件
                }
            }
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> createVideoSynthesis(Map<String, Object> params) {
        if (platformVideoConfig == null || StringUtils.isEmpty(platformVideoConfig.getServerUrl())) {
            throw new RuntimeException("视频合成配置不完整");  // 配置检查
        }
        String modelCode = String.valueOf(params.get(PARAM_MODEL));
        PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode); // 获取并记录模型价位信息
        String url = platformVideoConfig.getServerUrl().trim();
        if (!url.endsWith("/")) {
            url += "/";  // 构建完整URL - 确保正确拼接
        }
        url += platformVideoConfig.getSynthesisPath().trim().replaceFirst("^/", "");
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + platformVideoConfig.getApiKey());
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        try {
            String jsonBody = JSON.toJSONString(params);
            String response = HttpClientUtil.doPost(url, jsonBody, headers);
            Map<String, Object> apiResponse = JSON.parseObject(response);
            Map<String, Object> result = new HashMap<>();
            if (apiResponse != null) {
                result.put("code", apiResponse.getOrDefault("code", 500));
                result.put("msg", apiResponse.getOrDefault("msg", "未知错误"));
                Object dataObj = apiResponse.get("data"); // 获取data
                if (dataObj instanceof Map) {
                    Map<String, Object> data = (Map<String, Object>) dataObj;
                    Object taskId = data.get("task_id");  // 获取task_id
                    result.put("task_id", taskId != null ? taskId.toString() : "");
                    result.put("status", STATUS_PENDING);
                    // 添加模型价位信息到结果中
                    if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                        result.put("model_price", modelInfo.getModelVersion());
                    }
                } else {
                    result.put("task_id", "");
                    result.put("status", STATUS_FAILED);
                }
            } else {
                result.put("code", 500);
                result.put("msg", "API响应为空");
                result.put("task_id", "");
                result.put("status", STATUS_FAILED);
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException("创建视频合成任务失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> queryVideoSynthesis(String taskNo) {
        if (StringUtils.isEmpty(platformVideoConfig.getQueryPath())) {
            throw new ServiceException("查询接口路径未配置");
        }
        String url = platformVideoConfig.getServerUrl() + platformVideoConfig.getQueryPath();
        try {
            Map<String, Object> params = new HashMap<>();  // 构造查询参数
            params.put("model", "umi_video_v5");

            Map<String, Object> content = new HashMap<>();
            content.put("type", 2);
            content.put("task_id", taskNo);

            List<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            params.put("messages", messages);

            String jsonBody = JSON.toJSONString(params);
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + platformVideoConfig.getApiKey());
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            String response = HttpClientUtil.doPost(url, jsonBody, headers);  // 发送POST请求
            Map<String, Object> apiResponse = JSON.parseObject(response);
            Map<String, Object> result = new HashMap<>();  // 构造标准响应
            result.put("taskNo", taskNo);
            result.put("code", apiResponse.getOrDefault("code", 500));
            result.put("message", apiResponse.getOrDefault("msg", "未知错误"));
            
            Map<String, Object> data = (Map<String, Object>) apiResponse.get("data"); // 处理data部分
            if (data != null) {
                Integer status = (Integer) data.get("status");
                PlatformVideo queryTask = new PlatformVideo(); // 查询任务信息
                queryTask.setTaskNo(taskNo);
                List<PlatformVideo> tasks = selectPlatformVideoList(queryTask);
                PlatformVideo task = !tasks.isEmpty() ? tasks.get(0) : null;
                
                // 如果任务存在，添加模型价位信息到结果中
                if (task != null && StringUtils.isNotEmpty(task.getOperation())) {
                    try {
                        JSONObject operationJson = JSONObject.parseObject(task.getOperation());
                        if (operationJson.containsKey("model_price")) {
                            String modelPrice = operationJson.getString("model_price");
                            result.put("modelPrice", modelPrice);
                        } else {
                            log.warn("任务[{}]的操作JSON中未找到model_price字段", task.getId());
                        }
                    } catch (Exception e) {
                        log.warn("解析操作JSON失败: {}", e.getMessage());
                    }
                }
                switch (status) {
                    case 1:
                        result.put("status", STATUS_PENDING);
                        result.put("message", STATUS_DESC_PENDING);
                        break;
                    case 2:
                        result.put("status", STATUS_PROCESSING);
                        result.put("message", STATUS_DESC_PROCESSING);
                        break;
                    case 3: // 成功状态
                        String videoUrl = data.get("complete_url").toString();
                        try {
                            if (task != null && StringUtils.isEmpty(task.getResultVideo())) {
                                // 下载并保存视频，返回的是相对路径
                                String savedPath = downloadAndSaveVideo(videoUrl);
                                
                                result.put("status", STATUS_SUCCESS);
                                result.put("message", STATUS_DESC_SUCCESS);
                                result.put("originalStoragePath", savedPath);
                                
                                PlatformVideo updateTask = new PlatformVideo();
                                updateTask.setId(task.getId());
                                updateTask.setStatus(STATUS_SUCCESS);
                                updateTask.setVideoMessage(STATUS_DESC_SUCCESS);
                                // 关键修改：确保存储的是相对路径，不是临时URL
                                updateTask.setResultVideo(savedPath); // 这里必须是相对路径
                                Date nowDate = new Date();
                                updateTask.setCompleteAt(nowDate);
                                updateTask.setUpdatedAt(nowDate);
                                updateTask.setUpdateBy(SecurityUtils.getUsername());
                                platformVideoMapper.updatePlatformVideo(updateTask);
                                
                                // 生成临时访问URL仅供前端显示使用
                                String tempUrl = FileOperateUtils.getURL(savedPath);
                                result.put("videoUrl", tempUrl);
                                result.put("realVideoUrl", savedPath); // 真实的相对路径
                            } else if (task != null && StringUtils.isNotEmpty(task.getResultVideo())) {
                                // 任务已处理过，直接生成临时访问URL
                                result.put("status", STATUS_SUCCESS);
                                result.put("message", STATUS_DESC_SUCCESS);
                                result.put("originalStoragePath", task.getResultVideo());
                                
                                // 生成临时访问URL供前端显示
                                String tempUrl = FileOperateUtils.getURL(task.getResultVideo());
                                result.put("videoUrl", tempUrl);
                            }
                        } catch (Exception e) {
                            log.error("处理视频URL失败");
                        }
                        break;
                    case 4:
                        result.put("status", STATUS_FAILED);
                        result.put("message", STATUS_DESC_FAILED);
                        if (task != null) {
                            task.setStatus(STATUS_FAILED);
                            task.setVideoMessage(STATUS_DESC_FAILED);
                            task.setUpdateTime(new Date());
                            task.setUpdateBy(SecurityUtils.getUsername());
                            platformVideoMapper.updatePlatformVideo(task);
                        } else {
                            log.warn("无法找到任务ID为 {} 的记录", taskNo);
                        }
                        break;
                    default:
                        throw new ServiceException("未知状态码: " + status);
                }
                if (task != null) {
                    result.put("taskId", task.getId()); // 添加其他任务信息
                    result.put("model", task.getModel());
                    result.put("videoName", task.getNumber());
                    // 处理源文件URL
                    if (StringUtils.isNotEmpty(task.getDrivenVideo())) {
                        String videoTempUrl = FileOperateUtils.getURL(task.getDrivenVideo());
                        result.put("liveVideoUrl", videoTempUrl.toString());
                    }
                    if (StringUtils.isNotEmpty(task.getDrivenAudio())) {
                        String audioTempUrl = FileOperateUtils.getURL(task.getDrivenAudio());
                        result.put("liveSoundUrl", audioTempUrl.toString());
                    }
                    result.put("createTime", task.getCreateTime());
                    result.put("updateTime", task.getUpdateTime());
                }
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 创建任务  合成 H 版视频
     */
    @Override
    public Long synthesisH(PlatformVideo platformVideo) {
        platformVideo.setNumber(FileUrlUtils.generateVideoName()); //任务编号
        platformVideo.setCreateBy(SecurityUtils.getUsername()); //创建人
        platformVideo.setUpdateBy(SecurityUtils.getUsername()); //修改人
        platformVideo.setCreatedAt(DateUtils.getNowDate()); //创建时间
        platformVideo.setUpdatedAt(DateUtils.getNowDate()); //修改时间
        platformVideo.setVersion(PlatformVideoTaskVersion.H_VERSION); // 设置版本号
        platformVideoMapper.insertPlatformVideo(platformVideo);
        Long taskId = platformVideo.getId();
        return taskId;

    }

    /**
     * 数字人对话合成
     */
    @Override
    public List<Long> createDialogueSynthesis(DialogueSynthesisRequest request) {
        validateDialogueRequest(request);

        List<Long> videoTaskIds = new ArrayList<>();
        for (DialogueSynthesisRequest.DialogueContent dialogue : request.getDialogueContent()) {
            DialogueSynthesisRequest.DigitalHuman human = findHumanById(request.getDigitalHumans(), dialogue.getSpeaker());
            if (human == null) {
                throw new ServiceException("找不到发言人配置: " + dialogue.getSpeakerName());
            }

            Map<String, String> audioResult = generateAudioForText(dialogue.getText(), human);
            Long videoTaskId = createVideoTask(audioResult.get("audioUrl"), audioResult.get("audioMd5"),
                human, dialogue, request.getVersion(), request.getBboxShiftValue(), request.getModel());
            videoTaskIds.add(videoTaskId);
        }

        return videoTaskIds;
    }

    /**
     * 验证对话合成请求参数
     */
    private void validateDialogueRequest(DialogueSynthesisRequest request) {
        if (request.getDigitalHumans() == null || request.getDigitalHumans().isEmpty()) {
            throw new ServiceException("数字人配置不能为空");
        }
        if (request.getDialogueContent() == null || request.getDialogueContent().isEmpty()) {
            throw new ServiceException("对话内容不能为空");
        }

        // 验证数字人配置
        for (DialogueSynthesisRequest.DigitalHuman human : request.getDigitalHumans()) {
            if (StringUtils.isEmpty(human.getAvatarAddress()) || StringUtils.isEmpty(human.getVoiceName())) {
                throw new ServiceException("数字人配置不完整");
            }
            if ("system".equals(human.getVoiceType()) && human.getVoiceId() == null) {
                throw new ServiceException("系统声音ID不能为空");
            }
            if ("builtin".equals(human.getVoiceType())) {
                validateBuiltinSound(human.getVoiceName());
            }
        }

        // 验证版本和版本专用参数
        if (!"M".equalsIgnoreCase(request.getVersion()) && !"H".equalsIgnoreCase(request.getVersion()) && !"V".equalsIgnoreCase(request.getVersion())) {
            throw new ServiceException("不支持的版本类型: " + request.getVersion());
        }

        // M版参数验证
        if ("M".equalsIgnoreCase(request.getVersion()) && request.getBboxShiftValue() != null) {
            int bboxValue = request.getBboxShiftValue();
            if (bboxValue < -7 || bboxValue > 7) {
                throw new ServiceException("M版bboxShiftValue参数范围必须在-7到+7之间");
            }
        }

        // V版参数验证
        if ("V".equalsIgnoreCase(request.getVersion())) {
            if (StringUtils.isEmpty(request.getModel())) {
                throw new ServiceException("V版必须指定模型编码");
            }
            validateVModel(request.getModel());
        }
    }

    /**
     * 为单个文本生成音频
     */
    private Map<String, String> generateAudioForText(String text, DialogueSynthesisRequest.DigitalHuman human) {
        if ("system".equals(human.getVoiceType())) {
            return generateSystemAudio(text, human.getVoiceId());
        } else if ("builtin".equals(human.getVoiceType())) {
            return generateBuiltinAudio(text, human.getVoiceName());
        } else {
            throw new ServiceException("不支持的声音类型: " + human.getVoiceType());
        }
    }

    /**
     * 创建视频合成任务
     */
    private Long createVideoTask(String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String version, Integer bboxShiftValue, String modelCode) {

        if ("V".equalsIgnoreCase(version)) {
            return createVVersionTask(audioUrl, audioMd5, human, dialogue, modelCode);
        }

        PlatformVideo videoTask = new PlatformVideo();
        videoTask.setDrivenAudio(audioUrl);
        videoTask.setDrivenVideo(human.getAvatarAddress());
        videoTask.setStatus("1");

        // 设置操作信息
        JSONObject operation = new JSONObject();
        operation.put("dialogue_id", dialogue.getId());
        operation.put("speaker_id", dialogue.getSpeaker());
        operation.put("speaker_name", dialogue.getSpeakerName());
        operation.put("text", dialogue.getText());
        operation.put("avatar_name", human.getAvatarName());
        operation.put("voice_name", human.getVoiceName());
        operation.put("voice_type", human.getVoiceType());
        operation.put("task_type", "dialogue_synthesis_item");
        operation.put("driven_audio_md5", audioMd5);
        operation.put("driven_video_md5", getAvatarMd5(human.getAvatarAddress()));
        operation.put("model_price", getModelPriceByVersion(version));

        if ("M".equalsIgnoreCase(version) && bboxShiftValue != null) {
            operation.put("bbox_shift_value", bboxShiftValue);
        }

        videoTask.setOperationJson(operation);
        return "M".equalsIgnoreCase(version) ? add(videoTask) : synthesisH(videoTask);
    }

    /**
     * 创建V版视频合成任务
     */
    private Long createVVersionTask(String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
                                   DialogueSynthesisRequest.DialogueContent dialogue, String modelCode) {
        try {
            // 构建V版API请求参数，使用与现有V版接口相同的格式
            Map<String, Object> params = new HashMap<>();
            params.put(PARAM_MODEL, modelCode);

            // 添加video_name参数到顶层
            params.put("video_name", FileUrlUtils.generateVideoName());

            List<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");

            Map<String, Object> content = new HashMap<>();
            content.put("type", 1);
            content.put("live_sound_url", audioUrl); // 直接使用相对路径
            content.put("live_video_url", human.getAvatarAddress()); // 直接使用相对路径

            message.put("content", content);
            messages.add(message);
            params.put("messages", messages);

            log.info("V版API请求参数: {}", JSON.toJSONString(params));

            // 调用现有的V版API方法，它会自动处理URL转换
            Map<String, Object> apiResult = createVideoSynthesisWithUrls(params);

            log.info("V版API响应结果: {}", JSON.toJSONString(apiResult));

            // 保存V版任务到数据库
            return saveVVersionTask(params, modelCode, apiResult, audioUrl, audioMd5, human, dialogue);

        } catch (Exception e) {
            log.error("创建V版视频任务失败: {}", e.getMessage(), e);
            throw new ServiceException("创建V版视频任务失败: " + e.getMessage());
        }
    }

    /**
     * 保存V版任务到数据库
     */
    private Long saveVVersionTask(Map<String, Object> params, String modelCode, Map<String, Object> apiResult,
                                 String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
                                 DialogueSynthesisRequest.DialogueContent dialogue) {
        try {
            PlatformVideo task = new PlatformVideo();
            task.setModel(modelCode);

            // 从API响应中获取task_id
            String taskId = "";
            if (apiResult.containsKey("task_id")) {
                taskId = apiResult.get("task_id").toString();
            } else if (apiResult.containsKey("data") && apiResult.get("data") instanceof Map) {
                Map<String, Object> data = (Map<String, Object>) apiResult.get("data");
                if (data.containsKey("task_id")) {
                    taskId = data.get("task_id").toString();
                }
            }

            task.setTaskNo(taskId);
            task.setNumber(FileUrlUtils.generateVideoName());
            task.setVersion(PlatformVideoTaskVersion.V_VERSION);
            task.setStatus(apiResult.getOrDefault("status", STATUS_PENDING).toString());
            task.setDrivenAudio(audioUrl);
            task.setDrivenVideo(human.getAvatarAddress());

            String username = SecurityUtils.getUsername();
            Date nowDate = DateUtils.getNowDate();
            task.setCreateBy(username);
            task.setUpdateBy(username);
            task.setCreatedAt(nowDate);
            task.setUpdatedAt(nowDate);

            // 构建operation JSON
            PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
            JSONObject operation = new JSONObject();
            operation.put("dialogue_id", dialogue.getId());
            operation.put("speaker_id", dialogue.getSpeaker());
            operation.put("speaker_name", dialogue.getSpeakerName());
            operation.put("text", dialogue.getText());
            operation.put("avatar_name", human.getAvatarName());
            operation.put("voice_name", human.getVoiceName());
            operation.put("voice_type", human.getVoiceType());
            operation.put("task_type", "dialogue_synthesis_item");
            operation.put("driven_audio_md5", audioMd5);
            operation.put("driven_video_md5", getAvatarMd5(human.getAvatarAddress()));
            operation.put("video_priority", "0");
            operation.put("code", apiResult.getOrDefault("code", "200"));
            operation.put("video_message", apiResult.getOrDefault("msg", "任务创建成功"));

            if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                operation.put("model_price", modelInfo.getModelVersion());
            }

            task.setOperation(operation.toJSONString());

            int rows = platformVideoMapper.insertPlatformVideo(task);
            if (rows <= 0) {
                throw new ServiceException("保存V版任务记录失败");
            }

            return task.getId();
        } catch (Exception e) {
            throw new ServiceException("保存V版任务记录失败: " + e.getMessage());
        }
    }

    /**
     * 查找指定ID的数字人配置
     */
    private DialogueSynthesisRequest.DigitalHuman findHumanById(List<DialogueSynthesisRequest.DigitalHuman> humans, String humanId) {
        return humans.stream().filter(human -> humanId.equals(human.getId())).findFirst().orElse(null);
    }

    /**
     * 使用系统声音生成音频
     */
    private Map<String, String> generateSystemAudio(String text, Long voiceId) {
        try {
            log.info("调用系统音频合成 - 声音ID: {}, 文本: {}", voiceId, text.substring(0, Math.min(20, text.length())));

            // 通过SpringUtils获取PlatformTaskService
            Object platformTaskService = SpringUtils.getBean("platformTaskServiceImpl");
            if (platformTaskService == null) {
                throw new ServiceException("无法获取PlatformTaskService服务");
            }

            // 使用反射调用createTextToAudio方法
            java.lang.reflect.Method method = platformTaskService.getClass().getMethod("createTextToAudio", String.class, Long.class, Long.class);
            Long taskId = (Long) method.invoke(platformTaskService, text, voiceId, null);

            if (taskId == null) {
                throw new ServiceException("系统音频合成失败，未返回任务ID");
            }
            throw new ServiceException("系统音频合成功能暂未完全实现，需要等待音频合成完成的逻辑");
        } catch (Exception e) {
            throw new ServiceException("系统音频合成失败: " + e.getMessage());
        }
    }

    /**
     * 使用内置音色生成音频（阿里云）
     */
    private Map<String, String> generateBuiltinAudio(String text, String voiceName) {
        try {
            Object platformTaskService = SpringUtils.getBean("platformTaskServiceImpl");
            Object ttsRequest = createTtsRequest(text, voiceName);
            java.lang.reflect.Method method = platformTaskService.getClass().getMethod("synthesize", ttsRequest.getClass());
            String audioUrl = (String) method.invoke(platformTaskService, ttsRequest);

            if (StringUtils.isEmpty(audioUrl)) {
                throw new ServiceException("内置音色合成失败");
            }

            return getAudioInfoFromUrl(audioUrl);
        } catch (Exception e) {
            throw new ServiceException("内置音色合成失败: " + e.getMessage());
        }
    }

    /**
     * 创建TtsRequest对象
     */
    private Object createTtsRequest(String text, String voiceName) {
        try {
            // 通过反射创建TtsRequest对象
            Class<?> ttsRequestClass = Class.forName("com.ruoyi.platform.model.domain.TtsRequest");
            Object ttsRequest = ttsRequestClass.getDeclaredConstructor().newInstance();
            // 设置属性
            java.lang.reflect.Method setText = ttsRequestClass.getMethod("setText", String.class);
            setText.invoke(ttsRequest, text);
            java.lang.reflect.Method setVoice = ttsRequestClass.getMethod("setVoice", String.class);
            setVoice.invoke(ttsRequest, voiceName != null ? voiceName : "zhiyuan");
            java.lang.reflect.Method setFormat = ttsRequestClass.getMethod("setFormat", String.class);
            setFormat.invoke(ttsRequest, "wav");
            java.lang.reflect.Method setSampleRate = ttsRequestClass.getMethod("setSampleRate", int.class);
            setSampleRate.invoke(ttsRequest, 16000);
            java.lang.reflect.Method setVolume = ttsRequestClass.getMethod("setVolume", int.class);
            setVolume.invoke(ttsRequest, 50);
            return ttsRequest;
        } catch (Exception e) {
            log.error("创建TtsRequest对象失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从音频URL获取音频信息（路径和MD5）
     */
    private Map<String, String> getAudioInfoFromUrl(String audioUrl) {
        try {
            log.info("原始音频URL: {}", audioUrl);

            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(audioUrl);
            log.info("提取的文件路径: {}", filePath);

            // 先尝试从FileOperateUtils缓存获取MD5
            String audioMd5 = FileOperateUtils.getMd5ForFilePath(filePath);
            log.info("缓存获取的MD5: {}", audioMd5);

            // 如果缓存中没有，从数据库获取
            if (StringUtils.isEmpty(audioMd5)) {
                PlatformAudio audio = platformAudioMapper.getAudioDetailByAddress(filePath);
                if (audio != null) {
                    audioMd5 = audio.getAudioMd5();
                    log.info("数据库获取的MD5: {}", audioMd5);
                } else {
                    log.warn("数据库中未找到音频记录，路径: {}", filePath);
                }
            }

            Map<String, String> result = new HashMap<>();
            result.put("audioUrl", filePath);
            result.put("audioMd5", audioMd5);
            log.info("最终返回结果 - 路径: {}, MD5: {}", filePath, audioMd5);
            return result;
        } catch (Exception e) {
            log.error("获取音频信息失败: {}", e.getMessage(), e);
            throw new ServiceException("获取音频信息失败: " + e.getMessage());
        }
    }

    /**
     * 从URL中提取文件路径
     */
    private String extractFilePathFromUrl(String audioUrl) {
        try {
            String path = audioUrl;

            // 去掉协议部分 (http:// 或 https://)
            if (path.startsWith("http://") || path.startsWith("https://")) {
                path = path.substring(path.indexOf("://") + 3);
            }

            // 去掉域名部分，保留路径
            if (path.contains("/")) {
                path = path.substring(path.indexOf("/") + 1);
            }

            // 处理特殊情况：如果路径以plaform/开头，去掉plaform/
            if (path.startsWith("plaform/")) {
                path = path.substring(8);
            }

            // 去掉查询参数
            if (path.contains("?")) {
                path = path.substring(0, path.indexOf("?"));
            }

            // 确保路径以ModelTasks开头（标准化路径格式）
            if (!path.startsWith("ModelTasks/") && path.contains("ModelTasks/")) {
                path = path.substring(path.indexOf("ModelTasks/"));
            }

            return path;
        } catch (Exception e) {
            throw new ServiceException("解析音频URL失败: " + audioUrl);
        }
    }

    /**
     * 验证内置音色是否支持
     */
    private void validateBuiltinSound(String voiceName) {
        String[] supportedVoices = {
            "zhiyuan", "zhiyue", "zhisha", "zhida", "aiqi", "aicheng", "aijia",
            "siqi", "sijia", "mashu", "yueer", "ruoxi", "aida", "sicheng",
            "ninger", "xiaoyun", "xiaogang", "ruilin"
        };

        for (String supportedVoice : supportedVoices) {
            if (supportedVoice.equals(voiceName)) {
                return;
            }
        }
        throw new ServiceException("不支持的内置音色: " + voiceName);
    }

    /**
     * 验证V版模型是否可用
     */
    private void validateVModel(String modelCode) {
        PlatformModel model = platformModelMapper.selectWyModelByCode(modelCode);
        if (model == null) {
            throw new ServiceException("模型不存在: " + modelCode);
        }
        if (!model.getModelStatus().equals(1)) {
            throw new ServiceException("模型不可用: " + modelCode);
        }
    }

    /**
     * 根据版本获取模型价位
     */
    private String getModelPriceByVersion(String version) {
        if ("V".equalsIgnoreCase(version)) {
            return "600"; // V版默认价位，实际价位从模型信息获取
        } else {
            return "500"; // M版和H版价位
        }
    }

    /**
     * 获取形象MD5值
     */
    private String getAvatarMd5(String avatarAddress) {
        try {
            if (StringUtils.isEmpty(avatarAddress)) {
                return null;
            }
            Map<String, Object> condition = new java.util.HashMap<>();
            condition.put("imageAddress", avatarAddress);
            List<PlatformImage> images = platformImageMapper.selectImagesByCondition(condition);
            if (images != null && !images.isEmpty()) {
                return images.get(0).getMd5();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

}
