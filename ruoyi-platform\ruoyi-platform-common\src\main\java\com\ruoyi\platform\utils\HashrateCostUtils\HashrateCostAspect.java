package com.ruoyi.platform.utils.HashrateCostUtils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.platform.domain.PlatformHashrate;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.service.IPlatformHashrateService;
import com.ruoyi.platform.service.IPlatformModelService;
import com.ruoyi.platform.service.IPlatformVideoService;
import com.ruoyi.platform.utils.taskUtils.FileUrlUtils;

/**
 * 算力点业务扣除切面
 */
@Aspect
@Component
public class HashrateCostAspect {

    @Autowired
    private IPlatformHashrateService platformHashrateService;
    
    @Autowired
    private IPlatformVideoService platformVideoService;

    @Autowired
    private IPlatformModelService platformModelService;

    @Around("@annotation(com.ruoyi.platform.utils.HashrateCostUtils.HashrateCost)")
    public Object handleHashrateCost(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        HashrateCost annotation = method.getAnnotation(HashrateCost.class);
        if (annotation == null) {
            return joinPoint.proceed();
        }
        // 获取当前登录用户
        String userName = SecurityUtils.getUsername();
        Long hashrateId = platformHashrateService.getHashrateIdByUserId(userName);
        if (hashrateId == null) {
            throw new ServiceException("暂时未找到当前算力用户信息！");
        }
        // 如果是基于结果视频计费，则不在此处扣费
        if (annotation.resultVideoOnly()) {
            Object result = joinPoint.proceed();  // 执行业务逻辑
            // 处理视频合成任务
            if (result instanceof Map && ((Map<?, ?>) result).containsKey("id")) {
                Map<?, ?> resultMap = (Map<?, ?>) result;
                Object idObj = resultMap.get("id");
                if (idObj instanceof Number) {
                    Long taskId = ((Number) idObj).longValue();
                    PlatformVideo task = platformVideoService.selectPlatformVideoById(taskId);
                    if (task != null) {
                        JSONObject operation = task.getOperationJson();
                        // 判断是V版还是M版
                        boolean isVVersion = method.getName().equals("createVideo") || 
                                           FileUrlUtils.isVVersion(task);
                        // 设置费用相关信息
                        if (isVVersion && StringUtils.isNotEmpty(task.getModel())) {
                            // V版任务，从模型中获取价格
                            int modelPrice = platformModelService.getModelPrice(task.getModel());
                            operation.put("model_price", String.valueOf(modelPrice));
                            operation.put("hashrate_cost_unit", modelPrice);
                        } else {
                            // M版任务，使用固定价格500
                            operation.put("hashrate_cost_unit", annotation.unitCost());
                        }
                        // 通用字段
                        operation.put("hashrate_cost_title", annotation.description());
                        operation.put("version", isVVersion ? "V" : "M");
                        task.setOperationJson(operation);
                        task.setCreateBy(userName);
                        platformVideoService.updatePlatformVideo(task);
                    }
                }
            }
            return result;
        }
        int points = annotation.expectedPoints();  // 非结果视频计费，即时扣费的情况
        // 如果是动态计费
        if (annotation.dynamicCost() && joinPoint.getArgs().length > 0) {
            if (annotation.calculationType() == CostType.VIDEO_DURATION) {
                Object requestParam = joinPoint.getArgs()[0];
                Map<String, Object> durationInfo = extractVideoDurationInfo(requestParam);
                double estimatedDuration = (double) durationInfo.get("estimatedDuration");
                points = estimateVideoCost(estimatedDuration, annotation.unitCost());
            }
        }
        PlatformHashrate hashrate = platformHashrateService.selectPlatformHashrateByHashrateId(hashrateId);
        if (hashrate == null) {
            throw new ServiceException("算力用户不存在，联系管理员进行注册！");// 检查用户是否有足够算力点
        }
        long currentBalance = Long.parseLong(hashrate.getHashrateBalance());
        if (currentBalance < points) {
            throw new ServiceException("您算力点不足暂时无法使用此功能，请联系管理员进行充值！");
        }
        Object result = joinPoint.proceed();  // 执行业务并扣费
        platformHashrateService.deductHashratePoints(hashrateId, (long)points, annotation.description());
        return result;
    }

     // 估算视频合成的算力点消耗
    public int estimateVideoCost(double totalDuration, int unitCost) {
        int durationMinutes = (int) Math.ceil(totalDuration);
        return Math.max(1, durationMinutes * unitCost);
    }

    // 从请求参数中提取视频时长信息
    public Map<String, Object> extractVideoDurationInfo(Object requestParams) {
        Map<String, Object> result = new HashMap<>();
        double estimatedDuration = 1.0;
        result.put("estimatedDuration", estimatedDuration);
        result.put("success", true);
        return result;
    }
}
