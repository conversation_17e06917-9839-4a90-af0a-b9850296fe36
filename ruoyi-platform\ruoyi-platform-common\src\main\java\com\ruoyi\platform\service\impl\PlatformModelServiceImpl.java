package com.ruoyi.platform.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.mapper.PlatformModelMapper;
import com.ruoyi.platform.service.IPlatformHashrateService;
import com.ruoyi.platform.service.IPlatformModelService;
import com.ruoyi.platform.utils.taskUtils.FileUrlUtils;

/**
 * AI模型Service业务层处理
 */
@Service
public class PlatformModelServiceImpl implements IPlatformModelService 
{
    private static final Logger log = LoggerFactory.getLogger(PlatformModelServiceImpl.class);

    @Autowired
    private PlatformModelMapper platformModelMapper;
    
    @Autowired
    private IPlatformHashrateService platformHashrateService;
    
    // 默认价格点数
    private static final int DEFAULT_PRICE = 500;

    /**
     * 查询AI模型
     * 
     * @param modelId AI模型主键
     * @return AI模型
     */
    @Override
    public PlatformModel selectWyModelByModelId(Long modelId)
    {
        return platformModelMapper.selectWyModelByModelId(modelId);
    }

    /**
     * 查询AI模型列表
     * 
     * @param wyModel AI模型
     * @return AI模型
     */
    @Override
    public List<PlatformModel> selectWyModelList(PlatformModel wyModel)
    {
        return platformModelMapper.selectWyModelList(wyModel);
    }

    /**
     * 新增AI模型
     * 
     * @param wyModel AI模型
     * @return 结果
     */
    @Override
    public int insertWyModel(PlatformModel wyModel)
    {
        wyModel.setCreateTime(DateUtils.getNowDate());
        return platformModelMapper.insertWyModel(wyModel);
    }

    /**
     * 修改AI模型
     * 
     * @param wyModel AI模型
     * @return 结果
     */
    @Override
    public int updateWyModel(PlatformModel wyModel)
    {
        wyModel.setUpdateTime(DateUtils.getNowDate());
        return platformModelMapper.updateWyModel(wyModel);
    }

    /**
     * 批量删除AI模型
     * 
     * @param modelIds 需要删除的AI模型主键
     * @return 结果
     */
    @Override
    public int deleteWyModelByModelIds(Long[] modelIds)
    {
        return platformModelMapper.deleteWyModelByModelIds(modelIds);
    }

    /**
     * 删除AI模型信息
     * 
     * @param modelId AI模型主键
     * @return 结果
     */
    @Override
    public int deleteWyModelByModelId(Long modelId)
    {
        return platformModelMapper.deleteWyModelByModelId(modelId);
    }
    
    /**
     * 获取模型价格
     * @param modelCode 模型代码
     * @return 模型价格点数
     */
    @Override
    public int getModelPrice(String modelCode) {
        if (StringUtils.isEmpty(modelCode)) {
            return DEFAULT_PRICE;
        }
        
        PlatformModel model = platformModelMapper.selectWyModelByCode(modelCode);
        if (model != null && StringUtils.isNotEmpty(model.getModelVersion())) {
            try {
                return Integer.parseInt(model.getModelVersion());
            } catch (NumberFormatException e) {
                // 模型价格格式无效，使用默认价格
            }
        }
        
        return DEFAULT_PRICE;
    }
    
    /**
     * 从任务中获取模型价格
     * @param task 视频任务
     * @return 模型价格
     */
    public int getModelPriceFromTask(PlatformVideo task) {
        if (task == null) {
            return DEFAULT_PRICE;
        }
        
        // 尝试从operation中获取model_price
        if (StringUtils.isNotEmpty(task.getOperation())) {
            try {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                if (operation.containsKey("model_price")) {
                    String priceStr = operation.getString("model_price");
                    try {
                        return Integer.parseInt(priceStr);
                    } catch (NumberFormatException e) {
                        // 价格格式无效，继续尝试其他方法
                    }
                }
            } catch (Exception e) {
                // 解析失败，继续尝试其他方法
            }
        }
        
        // 从模型代码中获取价格
        if (StringUtils.isNotEmpty(task.getModel())) {
            return getModelPrice(task.getModel());
        }
        
        return DEFAULT_PRICE;
    }
    
    /**
     * 处理视频任务的费用扣除
     * @param task 视频任务
     * @return 是否扣除成功
     */
    @Override
    public boolean processTaskFeeDeduction(PlatformVideo task) {
        if (task == null || !"3".equals(task.getStatus())) {
            return false;
        }
        
        // 检查是否已扣费
        JSONObject operation = task.getOperationJson();
        if (operation != null && operation.getBooleanValue("fee_deducted", false)) {
            return true; // 已扣费，无需重复操作
        }
        
        // 获取创建者
        String userName = task.getCreateBy();
        if (StringUtils.isEmpty(userName)) {
            return false; // 创建者为空，无法扣费
        }
        
        // 查询算力账户ID
        Long hashrateId = platformHashrateService.getHashrateIdByUserId(userName);
        if (hashrateId == null) {
            return false; // 未找到算力账户，无法扣费
        }
        
        // 判断任务类型：V版还是M版
        boolean isVVersion = FileUrlUtils.isVVersion(task);
        
        // 确定基础价格点数和模型名称
        int basePoints;
        String modelName;
        
        if (isVVersion) {
            // V版：从operation中的model_price或模型中获取价格
            basePoints = getModelPriceFromTask(task);
            modelName = StringUtils.isNotEmpty(task.getModel()) ? task.getModel() : "V版模型";
        } else {
            // M版：使用固定价格500
            basePoints = DEFAULT_PRICE;
            modelName = "M版或H版视频合成";
        }
        
        // 计算视频时长 - 默认按1分钟计算
        int durationMinutes = 1; 
        
        // 计算总费用
        int totalPoints = basePoints * durationMinutes;
        
        // 确定描述信息
        String description = "视频合成(" + modelName + ")";
        
        try {
            // 执行扣费
            platformHashrateService.deductHashratePoints(
                hashrateId, 
                (long) totalPoints, 
                description
            );
            
            // 标记已扣费并保存实际扣除的点数
            if (operation == null) {
                operation = new JSONObject();
            }
            operation.put("fee_deducted", true);
            operation.put("fee_points", totalPoints);
            operation.put("fee_base_price", basePoints);
            operation.put("fee_time", System.currentTimeMillis());
            operation.put("version", isVVersion ? "V" : "M");
            
            // 将操作信息更新到任务中
            task.setOperationJson(operation);
            
            return true;
        } catch (Exception e) {
            log.error("任务[{}]扣费失败: {}", task.getId(), e.getMessage());
            return false;
        }
    }
}
