package com.ruoyi.platform.model.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.platform.domain.PlatformArticle;
import com.ruoyi.platform.model.domain.PlatformTask;
import com.ruoyi.platform.model.domain.TtsRequest;
import com.ruoyi.platform.model.service.IPlatformTaskService;
import com.ruoyi.platform.utils.HashrateCostUtils.HashrateCost;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

/**
 * 任务管理Controller
 * 
 * <AUTHOR>
 * @date 2024-09-24
 */
@RestController
@RequestMapping("/platform/task")
@Tag(name = "【任务管理】管理")
public class PlatformTaskController extends BaseController
{
    @Autowired
    private IPlatformTaskService platformTaskService;

    /**
     * 查询任务管理列表
     */
    @Operation(summary = "查询任务管理列表")
    //@PreAuthorize("@ss.hasPermi('platform:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Validated(value = { GetMapping.class }) PlatformTask platformTask)
    {
        startPage();
        List<PlatformTask> list = platformTaskService.selectPlatformTaskList(platformTask);
        return getDataTable(list);
    }

    /**
     * 导出任务管理列表
     */
    @Operation(summary = "导出任务管理列表")
    @PreAuthorize("@ss.hasPermi('platform:task:export')")
    @Log(title = "任务管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlatformTask platformTask)
    {
        List<PlatformTask> list = platformTaskService.selectPlatformTaskList(platformTask);
        ExcelUtil<PlatformTask> util = new ExcelUtil<PlatformTask>(PlatformTask.class);
        util.exportExcel(response, list, "任务管理数据");
    }

    /**
     * 获取任务管理详细信息
     */
    @Operation(summary = "获取任务管理详细信息")
    //@PreAuthorize("@ss.hasPermi('platform:task:query')")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId)
    {
        return success(platformTaskService.selectPlatformTaskByTaskId(taskId));
    }

    /**
     * 新增任务管理
     */
    @Operation(summary = "新增任务管理")
    //@PreAuthorize("@ss.hasPermi('platform:task:add')")
    @Log(title = "任务管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PlatformTask platformTask)
    {
        platformTask.setCreateBy(getUsername());
        platformTask.setUpdateBy(getUsername());
        return toAjax(platformTaskService.insertPlatformTask(platformTask));
    }

    /**
     * 修改任务管理
     */
    @Operation(summary = "修改任务管理")
    //@PreAuthorize("@ss.hasPermi('platform:task:edit')")
    @Log(title = "任务管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PlatformTask platformTask)
    {
        platformTask.setUpdateBy(getUsername());
        return toAjax(platformTaskService.updatePlatformTask(platformTask));
    }

    /**
     * 删除任务管理
     */
    @Operation(summary = "删除任务管理")
    //@PreAuthorize("@ss.hasPermi('platform:task:remove')")
    @Log(title = "任务管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable( name = "taskIds" ) Long[] taskIds) 
    {
        return toAjax(platformTaskService.deletePlatformTaskByTaskIds(taskIds));
    }

    // 获取成功的任务列表
    @Operation(summary = "获取任务管理列表带鉴权")
    //@PreAuthorize("@ss.hasPermi('platform:task:success')")
    @GetMapping("/success")
    public R<PlatformTask> getTasks(@PathVariable("machineCode") String machineCode) {
        PlatformTask task = platformTaskService.selectPlatformmachineCode(machineCode);
        return R.ok(task);
        
    }

    //创建一个声音推理任务
    @Operation(summary = "创建一个声音推理任务")
    @Log(title = "文案转音频", businessType = BusinessType.INSERT)
    @HashrateCost(description = "文案转音频", expectedPoints = 10)
    @PostMapping("/tts/audio")
    public AjaxResult createTTSAudio(@RequestBody PlatformArticle[] platformArticle, @RequestParam("model_name") Long modelName){
        return success(platformTaskService.createTTSAudio(platformArticle,modelName));
    }

    //创建一个训练声音任务
    @Operation(summary = "创建一个训练声音任务")
    @Log(title = "训练声音任务", businessType = BusinessType.INSERT)
    @PostMapping("/train/audio")
    public AjaxResult createTaskSAudio(@RequestParam("model_name") Long modelName,@RequestParam("train_path") String trainPath){
        return success(platformTaskService.createTaskSAudio(modelName,trainPath));
    }

    //创建文本转音频任务
    @Operation(summary = "创建文本转音频任务")
    //@Log(title = "文本转音频", businessType = BusinessType.INSERT)
    @HashrateCost(description = "文本转音频", expectedPoints = 10)
    @PostMapping("/text/audio")
    public AjaxResult createTextToAudio(@RequestParam("text") String text, @RequestParam("model_name") Long modelName, @RequestParam(value = "category_id", required = false) Long categoryId){
        return success(platformTaskService.createTextToAudio(text, modelName, categoryId));
    }

    /**
     * 语音合成API
     */
    @Operation(summary = "创建语音合成")
    @Log(title = "语音合成", businessType = BusinessType.INSERT)
    @PostMapping("/tts/yuyin")
    @Anonymous
    public AjaxResult createYuyin(@RequestBody TtsRequest request, HttpServletRequest httpRequest) {
        try {
            String audioUrl = platformTaskService.synthesize(request);
            return AjaxResult.success("语音合成成功", audioUrl);
        } catch (Exception e) {
            return AjaxResult.error("语音合成失败: " + e.getMessage());
        }
    }
}
