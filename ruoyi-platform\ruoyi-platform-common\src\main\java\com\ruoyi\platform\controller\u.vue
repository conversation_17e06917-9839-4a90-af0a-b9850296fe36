<template>
  <div class="dialogue-synthesis-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <el-icon class="header-icon"><VideoCamera /></el-icon>
        <h1 class="page-title">数字人对话合成</h1>
        <p class="page-description">创建多个数字人之间的对话视频，支持自定义形象、声音和对话内容</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 阶段切换标签 -->
      <el-tabs v-model="activeTab" class="synthesis-tabs" @tab-change="handleTabChange">
        <!-- 准备阶段 -->
        <el-tab-pane label="准备阶段" name="preparation" class="tab-pane">
          <div class="preparation-stage">
            <!-- 步骤指示器 -->
            <el-steps :active="currentStep" align-center class="preparation-steps">
              <el-step title="选择数字人形象" description="选择参与对话的数字人形象"></el-step>
              <el-step title="选择数字人声音" description="为每个数字人配置声音"></el-step>
              <el-step title="配置数字人信息" description="设置数字人名称和声音匹配"></el-step>
              <el-step title="编写对话内容" description="输入对话文本内容"></el-step>
            </el-steps>

            <!-- 步骤内容 -->
            <div class="step-content">
              <!-- 步骤1: 选择数字人形象 -->
              <div v-show="currentStep === 0" class="step-panel">
                <div class="panel-header">
                  <h3><el-icon><Picture /></el-icon>选择数字人形象</h3>
                  <p class="text-gray-500">请选择参与对话的数字人形象（建议选择2-4个）</p>
                </div>
                <div class="avatar-selection">
                  <div class="selection-controls flex justify-center gap-3 mb-5">
                    <el-button type="primary" size="large" @click="goToSelectAvatars"><el-icon><Plus /></el-icon>选择数字人形象</el-button>
                    <el-button type="default" @click="goToImagePage"><el-icon><Picture /></el-icon>管理形象素材</el-button>
                  </div>

                  <div class="selected-avatars">
                    <div class="section-header">
                      <h4>已选择的形象 ({{ selectedAvatars.length }}/6)</h4>
                      <p class="selection-tip text-gray-500">建议选择2-4个数字人形象以获得更好的对话效果</p>
                    </div>

                    <div v-if="selectedAvatars.length === 0" class="empty-selection">
                      <el-empty description="暂未选择任何数字人形象">
                        <el-button type="primary" @click="goToSelectAvatars">立即选择</el-button>
                      </el-empty>
                    </div>

                    <div v-else class="selected-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                      <div v-for="avatar in selectedAvatars" :key="avatar.imageId" class="selected-card relative rounded-lg overflow-hidden shadow-md border border-gray-200">
                        <div class="card-preview aspect-video bg-gray-100 relative">
                          <video v-if="avatarPreviews[avatar.imageAddress]" :src="avatarPreviews[avatar.imageAddress]" class="preview-video w-full h-full object-cover" muted loop autoplay></video>
                          <div v-else class="preview-placeholder absolute inset-0 flex flex-col items-center justify-center bg-gray-100">
                            <el-icon class="loading-icon text-gray-400 text-xl"><Loading /></el-icon>
                            <span class="text-gray-500 text-sm">加载中...</span>
                          </div>
                          <div class="card-overlay absolute top-2 right-2">
                            <el-button type="danger" size="small" circle @click="removeAvatar(avatar.imageId)" class="remove-btn opacity-80 hover:opacity-100 transition-opacity">
                              <el-icon><Close /></el-icon>
                            </el-button>
                          </div>
                        </div>
                        <div class="card-info p-3 bg-white">
                          <h5 class="font-medium text-sm truncate">{{ avatar.imageName }}</h5>
                          <p class="text-xs text-gray-500">{{ avatar.createBy }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 步骤2: 选择数字人声音 -->
              <div v-show="currentStep === 1" class="step-panel">
                <div class="panel-header">
                  <h3><el-icon><Microphone /></el-icon>选择数字人声音</h3>
                  <p class="text-gray-500">为每个数字人选择合适的声音</p>
                </div>
                <div class="voice-selection">
                  <div class="selection-controls flex justify-center gap-3 mb-5">
                    <el-button type="primary" size="large" @click="goToSelectVoices"><el-icon><Plus /></el-icon>选择数字人声音</el-button>
                    <el-button type="default" @click="goToSoundPage"><el-icon><Microphone /></el-icon>管理声音素材</el-button>
                  </div>

                  <div class="selected-voices">
                    <div class="section-header">
                      <h4>已选择的声音 ({{ selectedVoices.length }}/{{ selectedAvatars.length }})</h4>
                      <p class="selection-tip text-gray-500">需要为每个数字人选择一个声音</p>
                    </div>

                    <div v-if="selectedVoices.length === 0" class="empty-selection">
                      <el-empty description="暂未选择任何声音">
                        <el-button type="primary" @click="goToSelectVoices">立即选择</el-button>
                      </el-empty>
                    </div>

                    <div v-else class="selected-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                      <div v-for="voice in selectedVoices" :key="voice.soundId" class="selected-card rounded-lg overflow-hidden shadow-md border border-gray-200">
                        <div class="card-preview aspect-square bg-gray-100 flex items-center justify-center">
                          <div class="voice-avatar text-4xl text-gray-400">
                            <el-icon><Microphone /></el-icon>
                          </div>
                          <div class="card-overlay absolute top-2 right-2">
                            <el-button type="danger" size="small" circle @click="removeVoice(voice.soundId)" class="remove-btn opacity-80 hover:opacity-100 transition-opacity">
                              <el-icon><Close /></el-icon>
                            </el-button>
                          </div>
                        </div>
                        <div class="card-info p-3 bg-white">
                          <h5 class="font-medium text-sm mb-2">{{ voice.soundName }}</h5>
                          <audio :src="voice.soundRef" controls class="voice-player w-full"></audio>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 步骤3: 配置数字人信息 -->
              <div v-show="currentStep === 2" class="step-panel">
                <div class="panel-header">
                  <h3><el-icon><Edit /></el-icon>配置数字人信息</h3>
                  <p class="text-gray-500">为每个数字人设置名称并匹配声音</p>
                </div>
                <div class="human-config">
                  <div class="config-grid grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div v-for="(human, index) in digitalHumans" :key="human.id" class="human-config-card rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                      <div class="config-header bg-gray-50 p-3">
                        <h4 class="font-medium">数字人 {{ index + 1 }}</h4>
                      </div>
                      <div class="config-content flex flex-col md:flex-row p-4 gap-4">
                        <div class="avatar-preview flex-shrink-0 w-full md:w-24 aspect-square rounded-lg overflow-hidden">
                          <video :src="human.avatarAddress" class="config-video w-full h-full object-cover" muted loop autoplay></video>
                          <p class="avatar-name text-center text-sm mt-1 font-medium">{{ human.avatarName }}</p>
                        </div>
                        <div class="config-form flex-grow">
                          <el-form-item label="数字人名称">
                            <el-input v-model="human.name" placeholder="请输入数字人名称" maxlength="20" show-word-limit />
                          </el-form-item>
                          <el-form-item label="选择声音">
                            <el-select v-model="human.voiceId" placeholder="请选择声音" @change="updateVoiceName(human)">
                              <el-option v-for="voice in selectedVoices" :key="voice.soundId" :label="voice.soundName" :value="voice.soundId" />
                            </el-select>
                          </el-form-item>
                          <div v-if="human.voiceId" class="voice-preview">
                            <p class="text-sm text-gray-600">当前声音：{{ human.voiceName }}</p>
                            <audio :src="getVoiceRef(human.voiceId)" controls class="voice-player-small w-full mt-1" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 步骤4: 编写对话内容 -->
              <div v-show="currentStep === 3" class="step-panel">
                <div class="panel-header">
                  <h3><el-icon><Edit /></el-icon>编写对话内容</h3>
                  <p class="text-gray-500">设计数字人之间的对话内容</p>
                </div>
                <div class="dialogue-editor">
                  <div class="editor-toolbar flex flex-wrap justify-between items-center gap-3 mb-4">
                    <el-button type="primary" @click="addDialogue"><el-icon><Plus /></el-icon>添加对话</el-button>
                    <el-button @click="clearDialogue">清空对话</el-button>
                    <span class="dialogue-count text-gray-500">共 {{ dialogueContent.length }} 条对话</span>
                  </div>
                  <div class="dialogue-list space-y-3">
                    <div v-for="(dialogue, index) in dialogueContent" :key="dialogue.id" class="dialogue-item rounded-lg border border-gray-200 p-3">
                      <div class="dialogue-header flex items-center gap-3 mb-2">
                        <span class="dialogue-order bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs font-medium">
                          {{ index + 1 }}
                        </span>
                        <el-select v-model="dialogue.speaker" placeholder="选择发言人" @change="updateSpeakerName(dialogue)" class="speaker-select flex-grow">
                          <el-option v-for="human in digitalHumans" :key="human.id" :label="human.name" :value="human.id" />
                        </el-select>
                        <el-button type="danger" size="small" @click="removeDialogue(index)" :disabled="dialogueContent.length <= 1">
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                      <div class="dialogue-content">
                        <el-input v-model="dialogue.text" type="textarea" :rows="3" placeholder="请输入对话内容..." maxlength="500" show-word-limit />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤导航 -->
            <div class="step-navigation flex justify-center gap-4 mt-8">
              <el-button :disabled="currentStep === 0" @click="prevStep">上一步</el-button>
              <el-button type="primary" :disabled="!canNextStep" @click="nextStep">
                {{ currentStep === 3 ? '完成准备' : '下一步' }}
              </el-button>
            </div>
          </div>
        </el-tab-pane>

        <!-- 合成阶段 -->
        <el-tab-pane label="合成阶段" name="synthesis" class="tab-pane">
          <div class="synthesis-stage">
            <!-- 版本配置 -->
            <div class="version-config mb-8">
              <div class="config-header mb-4">
                <h3 class="text-xl font-semibold flex items-center gap-2"><el-icon><Setting /></el-icon>合成版本配置</h3>
                <p class="text-gray-500">选择合成版本并配置相关参数</p>
              </div>

              <div class="version-selection">
                <el-form :model="synthesisConfig" label-width="120px" class="config-form">
                  <el-form-item label="合成版本" required>
                    <el-radio-group v-model="synthesisConfig.version" @change="onVersionChange" class="version-radio-group">
                      <el-radio value="H" class="version-option">
                        <div class="version-card">
                          <div class="version-title">H版（默认版本）</div>
                          <div class="version-desc">标准合成版本，模型价位：500</div>
                          <div class="version-features">
                            <el-tag size="small" type="success">无需额外配置</el-tag>
                            <el-tag size="small" type="info">支持系统声音和内置音色</el-tag>
                          </div>
                        </div>
                      </el-radio>
                      <el-radio value="M" class="version-option">
                        <div class="version-card">
                          <div class="version-title">M版</div>
                          <div class="version-desc">支持边界框调整，模型价位：500</div>
                          <div class="version-features">
                            <el-tag size="small" type="warning">需要配置阈值</el-tag>
                            <el-tag size="small" type="info">支持边界框偏移</el-tag>
                          </div>
                        </div>
                      </el-radio>
                      <el-radio value="V" class="version-option">
                        <div class="version-card">
                          <div class="version-title">V版</div>
                          <div class="version-desc">高级版本，支持多种模型</div>
                          <div class="version-features">
                            <el-tag size="small" type="danger">需要选择模型</el-tag>
                            <el-tag size="small" type="success">动态价位</el-tag>
                          </div>
                        </div>
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <!-- M版专用配置 -->
                  <el-form-item v-if="synthesisConfig.version === 'M'" label="边界框偏移值" required>
                    <div class="bbox-config">
                      <el-slider
                        v-model="synthesisConfig.bboxShiftValue"
                        :min="-7"
                        :max="7"
                        :step="1"
                        show-stops
                        show-input
                        class="bbox-slider"
                      />
                      <div class="bbox-help">
                        <el-text size="small" type="info">
                          调整边界框偏移值（-7到+7），影响数字人在画面中的位置
                        </el-text>
                      </div>
                    </div>
                  </el-form-item>

                  <!-- V版专用配置 -->
                  <el-form-item v-if="synthesisConfig.version === 'V'" label="选择模型" required>
                    <div class="model-config">
                      <el-select
                        v-model="synthesisConfig.model"
                        placeholder="请选择V版模型"
                        @change="onModelChange"
                        class="model-select"
                      >
                        <el-option
                          v-for="model in availableModels"
                          :key="model.code"
                          :label="model.name"
                          :value="model.code"
                        >
                          <div class="model-option">
                            <span class="model-name">{{ model.name }}</span>
                            <span class="model-price">价位：{{ model.price }}</span>
                          </div>
                        </el-option>
                      </el-select>
                      <div class="model-help" v-if="selectedModel">
                        <el-text size="small" type="info">
                          {{ selectedModel.description }}
                        </el-text>
                      </div>
                    </div>
                  </el-form-item>

                  <!-- 配置预览 -->
                  <el-form-item label="配置预览">
                    <div class="config-preview">
                      <el-descriptions :column="2" border size="small">
                        <el-descriptions-item label="合成版本">
                          <el-tag :type="getVersionTagType(synthesisConfig.version)">
                            {{ getVersionName(synthesisConfig.version) }}
                          </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="预估价位">
                          <el-text type="primary" size="large">{{ getEstimatedPrice() }}</el-text>
                        </el-descriptions-item>
                        <el-descriptions-item v-if="synthesisConfig.version === 'M'" label="边界框偏移">
                          {{ synthesisConfig.bboxShiftValue }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="synthesisConfig.version === 'V'" label="选择模型">
                          {{ selectedModel ? selectedModel.name : '未选择' }}
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 合成任务概览 -->
            <div class="synthesis-overview mb-8">
              <div class="overview-header mb-4">
                <h3 class="text-xl font-semibold flex items-center gap-2"><el-icon><VideoCamera /></el-icon>对话合成任务</h3>
                <p class="text-gray-500">基于准备阶段的配置，自动生成数字人对话视频</p>
              </div>

              <!-- 任务配置预览 -->
              <div class="task-preview grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="preview-section">
                  <h4 class="font-medium mb-3 text-lg">参与数字人 ({{ digitalHumans.length }})</h4>
                  <div class="humans-preview grid grid-cols-2 sm:grid-cols-3 gap-3">
                    <div v-for="human in digitalHumans" :key="human.id" class="human-preview-card rounded-lg overflow-hidden border border-gray-200 shadow-sm">
                      <div class="human-avatar aspect-square bg-gray-100">
                        <video :src="human.avatarAddress" class="preview-video w-full h-full object-cover" muted loop autoplay></video>
                      </div>
                      <div class="human-info p-2 text-center">
                        <h5 class="font-medium text-sm truncate">{{ human.name }}</h5>
                        <p class="text-xs text-gray-500">{{ human.voiceName }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="preview-section">
                  <h4 class="font-medium mb-3 text-lg">对话内容 ({{ dialogueContent.length }} 条)</h4>
                  <div class="dialogue-preview space-y-2">
                    <div v-for="(dialogue, index) in dialogueContent.slice(0, 3)" :key="dialogue.id" class="dialogue-preview-item">
                      <span class="speaker-name font-medium text-gray-700">{{ dialogue.speakerName }}:</span>
                      <span class="dialogue-text text-gray-600 ml-1">{{ dialogue.text || '(未填写内容)' }}</span>
                    </div>
                    <div v-if="dialogueContent.length > 3" class="more-dialogues text-sm text-gray-500">
                      还有 {{ dialogueContent.length - 3 }} 条对话...
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 合成任务流程 -->
            <div class="synthesis-pipeline mb-8">
              <div class="pipeline-header mb-4">
                <h4 class="text-lg font-medium">合成流程</h4>
                <p class="text-gray-500">系统将按照以下步骤自动完成对话视频合成</p>
              </div>

              <div class="pipeline-steps space-y-4">
                <div class="pipeline-step flex items-start gap-4 p-3 border border-gray-200 rounded-lg">
                  <div class="step-icon flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                    <el-icon><Microphone /></el-icon>
                  </div>
                  <div class="step-content flex-grow">
                    <h5 class="font-medium">音频合成</h5>
                    <p class="text-sm text-gray-500">基于对话文本和选定声音生成音频文件</p>
                    <div class="step-status mt-1">
                      <el-tag v-if="synthesisTask.status === 'idle'" type="info">待执行</el-tag>
                      <el-tag v-else-if="synthesisTask.currentTask === 'audio'" type="warning">进行中</el-tag>
                      <el-tag v-else-if="isTaskCompleted('audio')" type="success">已完成</el-tag>
                      <el-tag v-else type="info">待执行</el-tag>
                    </div>
                  </div>
                </div>

                <div class="pipeline-step flex items-start gap-4 p-3 border border-gray-200 rounded-lg">
                  <div class="step-icon flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                  <div class="step-content flex-grow">
                    <h5 class="font-medium">视频合成</h5>
                    <p class="text-sm text-gray-500">基于音频文件和数字人形象生成视频片段</p>
                    <div class="step-status mt-1">
                      <el-tag v-if="synthesisTask.status === 'idle'" type="info">待执行</el-tag>
                      <el-tag v-else-if="synthesisTask.currentTask === 'video'" type="warning">进行中</el-tag>
                      <el-tag v-else-if="isTaskCompleted('video')" type="success">已完成</el-tag>
                      <el-tag v-else type="info">待执行</el-tag>
                    </div>
                  </div>
                </div>

                <div class="pipeline-step flex items-start gap-4 p-3 border border-gray-200 rounded-lg">
                  <div class="step-icon flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                    <el-icon><Edit /></el-icon>
                  </div>
                  <div class="step-content flex-grow">
                    <h5 class="font-medium">云剪辑合成</h5>
                    <p class="text-sm text-gray-500">将多个视频片段合成为完整的对话视频</p>
                    <div class="step-status mt-1">
                      <el-tag v-if="synthesisTask.status === 'idle'" type="info">待执行</el-tag>
                      <el-tag v-else-if="synthesisTask.currentTask === 'editing'" type="warning">进行中</el-tag>
                      <el-tag v-else-if="isTaskCompleted('editing')" type="success">已完成</el-tag>
                      <el-tag v-else type="info">待执行</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 任务控制 -->
            <div class="task-controls mb-8">
              <div class="controls-header mb-4">
                <h4 class="text-lg font-medium">任务控制</h4>
                <div class="task-progress" v-if="synthesisTask.status !== 'idle'">
                  <el-progress :percentage="synthesisTask.progress" :status="getProgressStatus()" :stroke-width="8" />
                  <p class="progress-text text-sm text-gray-500 mt-1">{{ synthesisTask.currentTask || '准备中...' }}</p>
                </div>
              </div>

              <div class="control-buttons flex flex-wrap justify-center gap-3">
                <el-button type="primary" size="large" :loading="synthesisTask.status === 'synthesizing'" :disabled="!canStartSynthesis" @click="startSynthesis">
                  <el-icon><VideoPlay /></el-icon>
                  {{ synthesisTask.status === 'synthesizing' ? '合成中...' : '开始合成' }}
                </el-button>
                <el-button v-if="synthesisTask.status === 'synthesizing'" type="danger" size="large" @click="stopSynthesis">
                  <el-icon><Close /></el-icon>停止合成
                </el-button>
                <el-button v-if="synthesisTask.status === 'completed'" type="success" size="large" @click="downloadResult">
                  <el-icon><Download /></el-icon>下载结果
                </el-button>
                <el-button size="large" @click="resetTask" :disabled="synthesisTask.status === 'synthesizing'">
                  重置任务
                </el-button>
              </div>
            </div>

            <!-- 合成结果 -->
            <div v-if="synthesisTask.results.length > 0" class="synthesis-results">
              <div class="results-header mb-4">
                <h4 class="text-lg font-medium">合成结果</h4>
                <p class="text-gray-500">已完成的合成任务结果</p>
              </div>

              <div class="results-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div v-for="result in synthesisTask.results" :key="result.id" class="result-card rounded-lg overflow-hidden border border-gray-200 shadow-sm">
                  <div class="result-preview aspect-video bg-gray-100">
                    <video v-if="result.type === 'video'" :src="result.url" class="result-video w-full h-full object-cover" controls></video>
                    <audio v-else-if="result.type === 'audio'" :src="result.url" class="result-audio w-full" controls></audio>
                  </div>
                  <div class="result-info p-3">
                    <h5 class="font-medium text-sm truncate">{{ result.name }}</h5>
                    <p class="text-xs text-gray-500 mt-1 line-clamp-2">{{ result.description }}</p>
                    <div class="result-actions flex gap-2 mt-3">
                      <el-button size="small" @click="previewResult(result)">预览</el-button>
                      <el-button size="small" @click="downloadSingle(result)">下载</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup name="DialogueSynthesis">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { imageDetail } from '@/api/platform/image'
import { VideoCamera,Picture,Microphone,Plus,Close,VideoPlay,Edit,Delete,Download,Loading,Setting} from '@element-plus/icons-vue'
const router = useRouter()
const route = useRoute()
const activeTab = ref('preparation')
const currentStep = ref(0)
const selectedAvatars = ref([]) // 数字人形象相关
const avatarPreviews = ref({})
const selectedVoices = ref([])// 数字人声音相关
const digitalHumans = ref([])// 数字人配置
const dialogueContent = ref([])// 对话内容

// 合成配置
const synthesisConfig = reactive({
  version: 'H', // H版、M版、V版
  bboxShiftValue: 0, // M版专用：边界框偏移值
  model: '', // V版专用：模型编码
})

// 可用模型列表
const availableModels = ref([
  {
    code: 'umi_video_v4',
    name: 'UMI Video V4 (V6模型)',
    price: '500',
    description: '标准V版模型，适合大多数场景使用'
  },
  {
    code: 'umi_video_v5',
    name: 'UMI Video V5 (V8模型)',
    price: '600',
    description: '高级V版模型，提供更好的合成质量'
  }
])

// 合成任务相关
const synthesisTask = reactive({
  status: 'idle', // idle, preparing, synthesizing, completed, failed
  progress: 0,
  currentTask: '',
  results: []
})

// 计算属性
const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0: // 选择形象
      return selectedAvatars.value.length >= 2
    case 1: // 选择声音
      return selectedVoices.value.length >= selectedAvatars.value.length
    case 2: // 配置数字人
      return digitalHumans.value.length === selectedAvatars.value.length &&
             digitalHumans.value.every(h => h.name && h.voiceId)
    case 3: // 编写对话
      return dialogueContent.value.length > 0 &&
             dialogueContent.value.every(d => d.speaker && d.text)
    default:
      return false
  }
})

// 选中的模型
const selectedModel = computed(() => {
  return availableModels.value.find(model => model.code === synthesisConfig.model)
})

// 版本配置相关方法
const onVersionChange = (version) => {
  // 切换版本时重置相关配置
  if (version !== 'M') {
    synthesisConfig.bboxShiftValue = 0
  }
  if (version !== 'V') {
    synthesisConfig.model = ''
  }

  // 设置默认值
  if (version === 'M') {
    synthesisConfig.bboxShiftValue = 0
  }
}

const onModelChange = (modelCode) => {
  const model = availableModels.value.find(m => m.code === modelCode)
  if (model) {
    ElMessage.success(`已选择模型：${model.name}`)
  }
}

const getVersionName = (version) => {
  const versionMap = {
    'H': 'H版（默认版本）',
    'M': 'M版',
    'V': 'V版'
  }
  return versionMap[version] || version
}

const getVersionTagType = (version) => {
  const typeMap = {
    'H': 'success',
    'M': 'warning',
    'V': 'danger'
  }
  return typeMap[version] || 'info'
}

const getEstimatedPrice = () => {
  if (synthesisConfig.version === 'V' && selectedModel.value) {
    return selectedModel.value.price
  }
  return '500' // H版和M版默认价位
}

// 方法
const handleTabChange = (tabName) => {
  if (tabName === 'synthesis' && currentStep.value < 4) {
    ElMessage.warning('请先完成准备阶段的所有步骤')
    activeTab.value = 'preparation'
    return
  }
}

const nextStep = async () => {
  // 检查当前步骤是否满足条件
  const stepValid = await validateCurrentStep()
  if (!stepValid) {
    return
  }

  if (currentStep.value < 3) {
    currentStep.value++

    // 自动处理下一个任务的准备工作
    if (currentStep.value === 2) {
      initializeDigitalHumans()
      ElMessage.success('已进入数字人配置阶段，请为每个数字人设置名称和声音')
    } else if (currentStep.value === 3) {
      initializeDialogue()
      ElMessage.success('已进入对话编写阶段，请设计数字人之间的对话内容')
    }
  } else {
    // 完成准备阶段，检查是否可以进入合成阶段
    const canProceed = await checkPreparationComplete()
    if (canProceed) {
      activeTab.value = 'synthesis'
      currentStep.value = 4
      ElMessage.success('准备阶段已完成，可以开始合成任务')
    }
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 路由跳转方法
const goToSelectAvatars = () => {
  // 跳转到形象选择页面
  router.push({
    path: '/szbVideo/image',
    query: {
      from: 'dialogueSynthesis',
      mode: 'select',
      returnPath: '/szbVideo/dialogueSynthesis'
    }
  })
}

const goToSelectVoices = () => {
  if (selectedAvatars.value.length === 0) {
    ElMessage.warning('请先选择数字人形象')
    return
  }

  // 跳转到声音模型选择页面
  router.push({
    path: '/szbSound/textAudio',
    query: {
      from: 'dialogueSynthesis',
      mode: 'select',
      returnPath: '/szbVideo/dialogueSynthesis',
      maxCount: selectedAvatars.value.length
    }
  })
}

const removeAvatar = (imageId) => {
  const index = selectedAvatars.value.findIndex(a => a.imageId === imageId)
  if (index > -1) {
    const avatar = selectedAvatars.value[index]

    ElMessageBox.confirm(
      `确定要移除数字人形象 "${avatar.imageName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      selectedAvatars.value.splice(index, 1)
      ElMessage.success(`已移除形象：${avatar.imageName}`)

      // 如果移除后数量不足，给出提示
      if (selectedAvatars.value.length < 2) {
        ElMessage.warning('建议至少选择2个数字人形象')
      }
    }).catch(() => {
      // 用户取消操作
    })
  }
}

const goToImagePage = () => {
  router.push({
    path: '/szbVideo/image',
    query: {
      from: 'dialogueSynthesis',
      returnPath: '/szbVideo/dialogueSynthesis'
    }
  })
}



const removeVoice = (soundId) => {
  const index = selectedVoices.value.findIndex(v => v.soundId === soundId)
  if (index > -1) {
    const voice = selectedVoices.value[index]

    ElMessageBox.confirm(
      `确定要移除声音 "${voice.soundName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      selectedVoices.value.splice(index, 1)
      ElMessage.success(`已移除声音：${voice.soundName}`)

      // 检查剩余声音数量
      const remaining = selectedAvatars.value.length - selectedVoices.value.length
      if (remaining > 0) {
        ElMessage.info(`还需要选择 ${remaining} 个声音`)
      }
    }).catch(() => {
      // 用户取消操作
    })
  }
}

const goToSoundPage = () => {
  router.push({
    path: '/szbSound/index',
    query: {
      from: 'dialogueSynthesis',
      returnPath: '/szbVideo/dialogueSynthesis'
    }
  })
}



// 获取形象预览
const getAvatarPreview = async (imageAddress) => {
  if (!imageAddress || avatarPreviews.value[imageAddress]) {
    return avatarPreviews.value[imageAddress]
  }

  try {
    const response = await imageDetail(imageAddress)
    if (response && response.data) {
      avatarPreviews.value[imageAddress] = response.data
      return response.data
    }
  } catch (error) {
    console.error('获取形象预览失败:', error)
  }
  return null
}

// 更新数字人配置
const updateDigitalHumans = async () => {
  console.log('开始更新数字人配置...', {
    avatars: selectedAvatars.value.length,
    voices: selectedVoices.value.length
  })

  // 基于选择的形象和声音更新数字人配置
  const newDigitalHumans = []

  for (let index = 0; index < selectedAvatars.value.length; index++) {
    const avatar = selectedAvatars.value[index]
    const existingHuman = digitalHumans.value.find(h => h.avatarId === avatar.imageId)
    const voice = selectedVoices.value[index]

    // 获取形象预览
    let avatarPreview = null
    if (avatar.imageAddress) {
      avatarPreview = await getAvatarPreview(avatar.imageAddress)
    }

    const humanConfig = {
      id: existingHuman?.id || `human_${Date.now()}_${index}`,
      avatarId: avatar.imageId,
      avatarName: avatar.imageName,
      avatarAddress: avatar.imageAddress,
      avatarPreview: avatarPreview,
      name: existingHuman?.name || `数字人${index + 1}`,
      voiceId: voice ? (voice.soundId || voice.value) : '',
      voiceName: voice ? (voice.soundName || voice.label) : '',
      voiceRef: voice ? voice.soundRef : ''
    }

    newDigitalHumans.push(humanConfig)
  }

  // 使用 nextTick 确保响应式更新
  await nextTick()
  digitalHumans.value = newDigitalHumans

  console.log('数字人配置更新完成:', digitalHumans.value)

  // 强制触发响应式更新
  await nextTick()
}

// 初始化数字人配置
const initializeDigitalHumans = () => {
  digitalHumans.value = selectedAvatars.value.map((avatar, index) => ({
    id: `human_${index + 1}`,
    name: `数字人${index + 1}`,
    avatarId: avatar.imageId,
    avatarName: avatar.imageName,
    avatarAddress: avatar.imageAddress,
    voiceId: selectedVoices.value[index]?.soundId || '',
    voiceName: selectedVoices.value[index]?.soundName || ''
  }))
}

// 初始化对话内容
const initializeDialogue = () => {
  if (dialogueContent.value.length === 0) {
    dialogueContent.value = [
      {
        id: 1,
        speaker: digitalHumans.value[0]?.id || '',
        speakerName: digitalHumans.value[0]?.name || '',
        text: '',
        order: 1
      }
    ]
  }
}

// 数字人配置相关方法
const updateVoiceName = (human) => {
  const voice = selectedVoices.value.find(v => v.soundId === human.voiceId)
  if (voice) {
    human.voiceName = voice.soundName
  }
}

const getVoiceRef = (voiceId) => {
  const voice = selectedVoices.value.find(v => v.soundId === voiceId)
  return voice ? voice.soundRef : ''
}

// 对话编辑相关方法
const addDialogue = () => {
  const newId = Math.max(...dialogueContent.value.map(d => d.id), 0) + 1
  dialogueContent.value.push({
    id: newId,
    speaker: digitalHumans.value[0]?.id || '',
    speakerName: digitalHumans.value[0]?.name || '',
    text: '',
    order: dialogueContent.value.length + 1
  })
}

const removeDialogue = (index) => {
  if (dialogueContent.value.length > 1) {
    dialogueContent.value.splice(index, 1)
    // 重新排序
    dialogueContent.value.forEach((dialogue, idx) => {
      dialogue.order = idx + 1
    })
  }
}

const clearDialogue = () => {
  ElMessageBox.confirm('确定要清空所有对话内容吗？', '确认清空', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    dialogueContent.value = [
      {
        id: 1,
        speaker: digitalHumans.value[0]?.id || '',
        speakerName: digitalHumans.value[0]?.name || '',
        text: '',
        order: 1
      }
    ]
    ElMessage.success('已清空对话内容')
  }).catch(() => {
    // 用户取消
  })
}

const updateSpeakerName = (dialogue) => {
  const human = digitalHumans.value.find(h => h.id === dialogue.speaker)
  if (human) {
    dialogue.speakerName = human.name
  }
}



// 合成阶段相关方法
const canStartSynthesis = computed(() => {
  // 基础条件检查
  const basicConditions = digitalHumans.value.length > 0 &&
         dialogueContent.value.length > 0 &&
         dialogueContent.value.every(d => d.speaker && d.text) &&
         synthesisTask.status !== 'synthesizing'

  if (!basicConditions) return false

  // 版本特定条件检查
  if (synthesisConfig.version === 'V') {
    return !!synthesisConfig.model // V版必须选择模型
  }

  return true
})

const isTaskCompleted = (taskName) => {
  return synthesisTask.results.some(r => r.taskType === taskName)
}

const getProgressStatus = () => {
  if (synthesisTask.status === 'completed') return 'success'
  if (synthesisTask.status === 'failed') return 'exception'
  return undefined
}

const startSynthesis = async () => {
  if (!canStartSynthesis.value) {
    ElMessage.warning('请先完成准备阶段的所有配置')
    return
  }

  // 版本特定验证
  if (synthesisConfig.version === 'V' && !synthesisConfig.model) {
    ElMessage.warning('V版必须选择模型')
    return
  }

  try {
    synthesisTask.status = 'synthesizing'
    synthesisTask.progress = 0
    synthesisTask.results = []
    synthesisTask.currentTask = '准备合成任务...'

    // 检查素材准备情况
    const materialsReady = await checkMaterialsReady()
    if (!materialsReady) {
      ElMessage.error('素材检查失败，请确保所有素材都已准备就绪')
      synthesisTask.status = 'failed'
      return
    }

    // 调用数字人对话合成API
    await executeDialogueSynthesis()

    synthesisTask.status = 'completed'
    synthesisTask.progress = 100
    synthesisTask.currentTask = '合成完成'

    ElMessage.success('对话视频合成完成！')

  } catch (error) {
    console.error('合成失败:', error)
    synthesisTask.status = 'failed'
    synthesisTask.currentTask = '合成失败'
    ElMessage.error('合成失败: ' + (error.message || '未知错误'))
  }
}

const stopSynthesis = () => {
  ElMessageBox.confirm('确定要停止当前合成任务吗？', '确认停止', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    synthesisTask.status = 'idle'
    synthesisTask.progress = 0
    synthesisTask.currentTask = ''
    ElMessage.success('已停止合成任务')
  }).catch(() => {
    // 用户取消
  })
}

const resetTask = () => {
  synthesisTask.status = 'idle'
  synthesisTask.progress = 0
  synthesisTask.currentTask = ''
  synthesisTask.results = []
  ElMessage.success('任务已重置')
}

const downloadResult = () => {
  // 下载最终合成结果
  const finalResult = synthesisTask.results.find(r => r.taskType === 'final')
  if (finalResult) {
    const link = document.createElement('a')
    link.href = finalResult.url
    link.download = finalResult.name
    link.click()
    ElMessage.success('开始下载合成结果')
  } else {
    ElMessage.warning('暂无可下载的结果')
  }
}

const downloadSingle = (result) => {
  const link = document.createElement('a')
  link.href = result.url
  link.download = result.name
  link.click()
  ElMessage.success(`开始下载: ${result.name}`)
}

const previewResult = (result) => {
  // 预览结果 - 可以打开模态框或新窗口
  window.open(result.url, '_blank')
}

// 合成任务执行方法
const checkMaterialsReady = async () => {
  // 检查数字人形象是否可用
  for (const human of digitalHumans.value) {
    if (!human.avatarAddress || !human.voiceId) {
      ElMessage.error(`数字人 ${human.name} 的素材不完整`)
      return false
    }
  }

  // 检查对话内容是否完整
  for (const dialogue of dialogueContent.value) {
    if (!dialogue.speaker || !dialogue.text) {
      ElMessage.error('存在未完成的对话内容')
      return false
    }
  }

  // 检查版本配置
  if (synthesisConfig.version === 'V' && !synthesisConfig.model) {
    ElMessage.error('V版必须选择模型')
    return false
  }

  return true
}

// 执行数字人对话合成 - 分步骤执行
const executeDialogueSynthesis = async () => {
  synthesisTask.currentTask = '开始合成流程...'
  synthesisTask.progress = 5

  try {
    // 步骤1: 音频合成
    await executeAudioSynthesis()

    // 步骤2: 视频合成
    await executeVideoSynthesis()

    // 步骤3: 云剪辑合成（暂时跳过）
    // await executeCloudEditing()

  } catch (error) {
    console.error('合成流程失败:', error)
    throw error
  }
}

// 执行音频合成
const executeAudioSynthesis = async () => {
  synthesisTask.currentTask = '正在进行音频合成...'
  synthesisTask.progress = 10

  const audioTasks = []

  try {
    // 为每个对话内容生成音频
    for (let i = 0; i < dialogueContent.value.length; i++) {
      const dialogue = dialogueContent.value[i]
      const human = digitalHumans.value.find(h => h.id === dialogue.speaker)

      if (!human) {
        throw new Error(`找不到发言人配置: ${dialogue.speakerName}`)
      }

      synthesisTask.currentTask = `正在合成音频 ${i + 1}/${dialogueContent.value.length}...`
      synthesisTask.progress = 10 + (i / dialogueContent.value.length) * 30

      // 调用音频合成API
      const audioResult = await synthesizeAudio(dialogue.text, human)
      audioTasks.push({
        dialogueId: dialogue.id,
        audioUrl: audioResult.audioUrl,
        audioMd5: audioResult.audioMd5,
        human: human,
        dialogue: dialogue
      })
    }

    synthesisTask.currentTask = '音频合成完成'
    synthesisTask.progress = 40

    // 保存音频任务结果供视频合成使用
    synthesisTask.audioTasks = audioTasks

  } catch (error) {
    console.error('音频合成失败:', error)
    throw new Error('音频合成失败: ' + error.message)
  }
}

// 执行视频合成
const executeVideoSynthesis = async () => {
  synthesisTask.currentTask = '正在进行视频合成...'
  synthesisTask.progress = 45

  const videoTaskIds = []

  try {
    const audioTasks = synthesisTask.audioTasks || []

    // 为每个音频任务创建视频合成任务
    for (let i = 0; i < audioTasks.length; i++) {
      const audioTask = audioTasks[i]

      synthesisTask.currentTask = `正在创建视频任务 ${i + 1}/${audioTasks.length}...`
      synthesisTask.progress = 45 + (i / audioTasks.length) * 20

      // 构建视频合成请求
      const videoRequest = {
        drivenAudio: audioTask.audioUrl,
        drivenVideo: audioTask.human.avatarAddress,
        version: synthesisConfig.version
      }

      // 添加版本特定参数
      if (synthesisConfig.version === 'M') {
        videoRequest.bboxShiftValue = synthesisConfig.bboxShiftValue
      } else if (synthesisConfig.version === 'V') {
        videoRequest.model = synthesisConfig.model
      }

      // 调用视频合成API
      const videoResult = await createVideoTask(videoRequest, audioTask)
      videoTaskIds.push(videoResult.taskId)
    }

    synthesisTask.currentTask = '视频任务创建完成，正在处理...'
    synthesisTask.progress = 65

    // 轮询检查视频任务状态
    await pollVideoTaskStatus(videoTaskIds)

  } catch (error) {
    console.error('视频合成失败:', error)
    throw new Error('视频合成失败: ' + error.message)
  }
}

// 音频合成API调用
const synthesizeAudio = async (text, human) => {
  try {
    const response = await fetch('/platform/task/synthesize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text: text,
        voice: human.voiceName || 'zhiyuan',
        format: 'wav',
        sampleRate: 16000,
        volume: 50
      })
    })

    const result = await response.json()

    if (result.code === 200) {
      // 从返回的URL中提取文件路径和MD5
      return await getAudioInfoFromResponse(result.data)
    } else {
      throw new Error(result.msg || '音频合成失败')
    }
  } catch (error) {
    console.error('音频合成API调用失败:', error)
    throw error
  }
}

// 视频任务创建API调用
const createVideoTask = async (videoRequest, audioTask) => {
  try {
    let apiUrl = ''

    // 根据版本选择不同的API
    if (synthesisConfig.version === 'M') {
      apiUrl = '/platform/video/add'
    } else if (synthesisConfig.version === 'V') {
      apiUrl = '/platform/video/createVideoSynthesisWithUrls'
    } else {
      // H版
      apiUrl = '/platform/video/synthesisH'
    }

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(videoRequest)
    })

    const result = await response.json()

    if (result.code === 200) {
      return {
        taskId: result.data.id || result.data.taskId,
        audioTask: audioTask
      }
    } else {
      throw new Error(result.msg || '视频任务创建失败')
    }
  } catch (error) {
    console.error('视频任务创建API调用失败:', error)
    throw error
  }
}

// 轮询视频任务状态
const pollVideoTaskStatus = async (taskIds) => {
  const maxAttempts = 60 // 最多轮询60次
  let attempts = 0

  const checkStatus = async () => {
    attempts++
    synthesisTask.currentTask = `正在检查视频任务状态... (${attempts}/${maxAttempts})`
    synthesisTask.progress = 65 + (attempts / maxAttempts) * 30

    try {
      // 检查所有任务的状态
      const statusPromises = taskIds.map(taskId =>
        fetch(`/platform/video/status/${taskId}`).then(res => res.json())
      )

      const statusResults = await Promise.all(statusPromises)

      // 检查是否所有任务都完成
      const allCompleted = statusResults.every(result =>
        result.code === 200 && result.data.status === '3' // 3表示完成
      )

      if (allCompleted) {
        // 所有任务完成，收集结果
        statusResults.forEach((result, index) => {
          if (result.data.resultVideo) {
            synthesisTask.results.push({
              id: `video_${index}`,
              name: `对话片段_${index + 1}.mp4`,
              description: `数字人对话视频片段 ${index + 1}`,
              type: 'video',
              url: result.data.resultVideo,
              taskType: 'video'
            })
          }
        })

        synthesisTask.progress = 95
        synthesisTask.currentTask = '视频合成完成'
        return true
      }

      // 检查是否有失败的任务
      const hasFailed = statusResults.some(result =>
        result.code === 200 && result.data.status === '4' // 4表示失败
      )

      if (hasFailed) {
        throw new Error('部分视频任务执行失败')
      }

      // 继续轮询
      if (attempts < maxAttempts) {
        setTimeout(checkStatus, 3000) // 3秒后再次检查
      } else {
        throw new Error('视频任务执行超时')
      }

    } catch (error) {
      console.error('视频状态检查失败:', error)
      throw error
    }
  }

  await checkStatus()
}

// 从音频合成响应中获取音频信息
const getAudioInfoFromResponse = async (audioUrl) => {
  try {
    // 从URL中提取文件路径
    const filePath = extractFilePathFromUrl(audioUrl)

    // 获取音频MD5（可能需要调用额外的API或从缓存获取）
    const audioMd5 = await getAudioMd5(filePath)

    return {
      audioUrl: filePath,
      audioMd5: audioMd5
    }
  } catch (error) {
    console.error('获取音频信息失败:', error)
    throw error
  }
}

// 获取音频MD5
const getAudioMd5 = async (filePath) => {
  try {
    const response = await fetch(`/platform/audio/md5?path=${encodeURIComponent(filePath)}`)
    const result = await response.json()

    if (result.code === 200) {
      return result.data.md5
    } else {
      console.warn('获取音频MD5失败，使用空值')
      return ''
    }
  } catch (error) {
    console.warn('获取音频MD5失败:', error)
    return ''
  }
}

// 从URL中提取文件路径
const extractFilePathFromUrl = (audioUrl) => {
  try {
    let path = audioUrl

    // 去掉协议部分 (http:// 或 https://)
    if (path.startsWith('http://') || path.startsWith('https://')) {
      path = path.substring(path.indexOf('://') + 3)
    }

    // 去掉域名部分，保留路径
    if (path.includes('/')) {
      path = path.substring(path.indexOf('/') + 1)
    }

    // 处理特殊情况：如果路径以plaform/开头，去掉plaform/
    if (path.startsWith('plaform/')) {
      path = path.substring(8)
    }

    // 去掉查询参数
    if (path.includes('?')) {
      path = path.substring(0, path.indexOf('?'))
    }

    // 确保路径以ModelTasks开头（标准化路径格式）
    if (!path.startsWith('ModelTasks/') && path.includes('ModelTasks/')) {
      path = path.substring(path.indexOf('ModelTasks/'))
    }

    return path
  } catch (error) {
    throw new Error('解析音频URL失败: ' + audioUrl)
  }
}

const executeAudioSynthesis = async () => {
  synthesisTask.currentTask = '正在生成音频...'
  synthesisTask.progress = 10

  // 模拟音频合成过程
  for (let i = 0; i < dialogueContent.value.length; i++) {
    const dialogue = dialogueContent.value[i]
    const human = digitalHumans.value.find(h => h.id === dialogue.speaker)

    synthesisTask.currentTask = `正在合成 ${human.name} 的音频 (${i + 1}/${dialogueContent.value.length})`
    synthesisTask.progress = 10 + (i + 1) / dialogueContent.value.length * 30

    // 这里应该调用实际的音频合成API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟延迟

    // 添加音频合成结果
    synthesisTask.results.push({
      id: `audio_${i}`,
      name: `${human.name}_音频_${i + 1}.mp3`,
      description: `${human.name}: ${dialogue.text.substring(0, 20)}...`,
      type: 'audio',
      url: '/mock-audio-url.mp3', // 实际应该是合成后的音频URL
      taskType: 'audio'
    })
  }
}

const executeVideoSynthesis = async () => {
  synthesisTask.currentTask = '正在生成视频...'
  synthesisTask.progress = 40

  // 模拟视频合成过程
  for (let i = 0; i < dialogueContent.value.length; i++) {
    const dialogue = dialogueContent.value[i]
    const human = digitalHumans.value.find(h => h.id === dialogue.speaker)

    synthesisTask.currentTask = `正在合成 ${human.name} 的视频 (${i + 1}/${dialogueContent.value.length})`
    synthesisTask.progress = 40 + (i + 1) / dialogueContent.value.length * 40

    // 这里应该调用实际的视频合成API
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟延迟

    // 添加视频合成结果
    synthesisTask.results.push({
      id: `video_${i}`,
      name: `${human.name}_视频_${i + 1}.mp4`,
      description: `${human.name}: ${dialogue.text.substring(0, 20)}...`,
      type: 'video',
      url: '/mock-video-url.mp4', // 实际应该是合成后的视频URL
      taskType: 'video'
    })
  }
}

const executeCloudEditing = async () => {
  synthesisTask.currentTask = '正在进行云剪辑合成...'
  synthesisTask.progress = 80

  // 模拟云剪辑过程
  await new Promise(resolve => setTimeout(resolve, 2000))

  synthesisTask.progress = 95
  synthesisTask.currentTask = '正在生成最终视频...'

  await new Promise(resolve => setTimeout(resolve, 1000))

  // 添加最终合成结果
  synthesisTask.results.push({
    id: 'final_video',
    name: '数字人对话视频.mp4',
    description: `完整的对话视频，包含 ${digitalHumans.value.length} 个数字人，${dialogueContent.value.length} 段对话`,
    type: 'video',
    url: '/mock-final-video-url.mp4', // 实际应该是最终合成视频的URL
    taskType: 'final'
  })
}





// 任务管理和状态检查方法
const validateCurrentStep = async () => {
  switch (currentStep.value) {
    case 0: // 选择形象
      if (selectedAvatars.value.length < 2) {
        ElMessage.warning('请至少选择2个数字人形象')
        return false
      }
      if (selectedAvatars.value.length > 6) {
        ElMessage.warning('最多只能选择6个数字人形象')
        return false
      }
      break

    case 1: // 选择声音
      if (selectedVoices.value.length < selectedAvatars.value.length) {
        ElMessage.warning(`请为所有数字人选择声音，还需要选择 ${selectedAvatars.value.length - selectedVoices.value.length} 个声音`)
        return false
      }
      break

    case 2: // 配置数字人
      const incompleteHumans = digitalHumans.value.filter(h => !h.name || !h.voiceId)
      if (incompleteHumans.length > 0) {
        ElMessage.warning('请完成所有数字人的名称和声音配置')
        return false
      }

      // 检查名称是否重复
      const names = digitalHumans.value.map(h => h.name)
      const uniqueNames = [...new Set(names)]
      if (names.length !== uniqueNames.length) {
        ElMessage.warning('数字人名称不能重复')
        return false
      }
      break

    case 3: // 编写对话
      if (dialogueContent.value.length === 0) {
        ElMessage.warning('请至少添加一条对话')
        return false
      }

      const incompleteDialogues = dialogueContent.value.filter(d => !d.speaker || !d.text.trim())
      if (incompleteDialogues.length > 0) {
        ElMessage.warning('请完成所有对话的发言人和内容设置')
        return false
      }
      break
  }

  return true
}

const checkPreparationComplete = async () => {
  // 综合检查所有准备阶段的要求
  const checks = [
    {
      condition: selectedAvatars.value.length >= 2,
      message: '请至少选择2个数字人形象'
    },
    {
      condition: selectedVoices.value.length >= selectedAvatars.value.length,
      message: '请为所有数字人选择声音'
    },
    {
      condition: digitalHumans.value.every(h => h.name && h.voiceId),
      message: '请完成所有数字人的配置'
    },
    {
      condition: dialogueContent.value.length > 0 && dialogueContent.value.every(d => d.speaker && d.text.trim()),
      message: '请完成对话内容的编写'
    }
  ]

  for (const check of checks) {
    if (!check.condition) {
      ElMessage.error(check.message)
      return false
    }
  }

  return true
}

// 自动任务继续处理
const checkAndProceedNextTask = async () => {
  // 在合成过程中，检查是否可以自动进行下一个任务
  if (synthesisTask.status === 'synthesizing') {
    return // 正在合成中，不需要检查
  }

  // 检查是否有未完成的任务需要继续
  const audioCompleted = synthesisTask.results.some(r => r.taskType === 'audio')
  const videoCompleted = synthesisTask.results.some(r => r.taskType === 'video')
  const editingCompleted = synthesisTask.results.some(r => r.taskType === 'final')

  if (!audioCompleted && synthesisTask.status === 'idle') {
    // 可以开始音频合成
    //ElMessage.info('检测到可以开始音频合成任务')
  } else if (audioCompleted && !videoCompleted && synthesisTask.status === 'idle') {
    // 可以开始视频合成
    ElMessage.info('音频合成已完成，可以开始视频合成任务')
  } else if (videoCompleted && !editingCompleted && synthesisTask.status === 'idle') {
    // 可以开始云剪辑
    ElMessage.info('视频合成已完成，可以开始云剪辑任务')
  }
}

// 处理从其他页面返回的选择结果
const handleReturnFromSelection = async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const selectedData = urlParams.get('selectedData')
  const selectionType = urlParams.get('type')

  if (selectedData && selectionType) {
    try {
      const data = JSON.parse(decodeURIComponent(selectedData))

      if (selectionType === 'avatars') {
        // 处理形象选择结果
        selectedAvatars.value = data.map(item => ({
          imageId: item.imageId,
          imageName: item.imageName,
          imageAddress: item.imageAddress,
          createBy: item.createBy,
          createTime: item.createTime
        }))

        // 立即清除URL参数，避免刷新时重复处理
        const newUrl = window.location.pathname
        window.history.replaceState({}, document.title, newUrl)

        ElMessage.success(`已选择 ${data.length} 个数字人形象`)

        // 强制更新数字人配置
        await updateDigitalHumans()

        // 强制触发响应式更新
        selectedAvatars.value = [...selectedAvatars.value]

      } else if (selectionType === 'voices') {
        // 处理声音选择结果 - 兼容不同的数据结构
        selectedVoices.value = data.map(item => ({
          soundId: item.audioId || item.soundId || item.value,
          soundName: item.content || item.soundName || item.label,
          soundRef: item.audioPath || item.soundRef || '',
          createBy: item.createBy || 'system',
          createTime: item.createTime || new Date().toISOString()
        }))

        // 立即清除URL参数，避免刷新时重复处理
        const newUrl = window.location.pathname
        window.history.replaceState({}, document.title, newUrl)

        ElMessage.success(`已选择 ${data.length} 个声音`)

        // 强制更新数字人配置
        await updateDigitalHumans()

        // 强制触发响应式更新
        selectedVoices.value = [...selectedVoices.value]
      }

    } catch (error) {
      console.error('处理返回数据失败:', error)
      ElMessage.error('处理选择结果失败')

      // 出错时也要清除URL参数
      const newUrl = window.location.pathname
      window.history.replaceState({}, document.title, newUrl)
    }
  }
}

// 监听路由查询参数变化，实时处理返回数据
watch(() => route.query, async (newQuery) => {
  if (newQuery.selectedData && newQuery.type) {
    await nextTick() // 确保DOM更新完成
    await handleReturnFromSelection()
  }
}, { immediate: true, deep: true })

// 生命周期
onMounted(async () => {
  // 处理从其他页面返回的选择结果
  await handleReturnFromSelection()

  // 定期检查任务状态
  setInterval(checkAndProceedNextTask, 5000)
})
</script>