package com.ruoyi.platform.model.domain;

import lombok.Data;

/**
 * 语音合成请求DTO
 */
@Data
public class TtsRequest {
    private String text;           // 合成文本（必填）
    private String format = "wav"; // 音频格式：wav, mp3, pcm
    private int sampleRate = 16000; // 采样率：8000, 16000, 22050, 24000
    private String voice = "siyue"; // 发音人
    private int volume = 50;       // 音量 0-100
    private Integer speechRate = 0; // 语速 -500~500
    private int pitchRate = 0;     // 语调 -500~500
    private String filePath;       // 上传路径（可选）
}
